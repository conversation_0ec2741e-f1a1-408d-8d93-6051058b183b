import 'dart:convert';
import 'package:darve/config.dart';
import 'package:darve/mobx/AuthStore/auth_store.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/init.dart';
import 'package:darve/utils/interfaces/Post.dart';
import 'package:http/http.dart' as http;

Future<List<Post>> getFollowingPosts() async {
  try {
    final AuthStore authStore = Darve.instance.authStore;

    final jwtToken = authStore.jwtToken;
    if (jwtToken == null || jwtToken.isEmpty) {
      throw Exception('getFollowingPosts JWT Token is missing.');
    }

    final response = await http.get(
      Uri.parse('${AppConfig.instance.apiUrl}/u/following/posts'),
      headers: {
        'Accept': 'application/json',
        'Cookie': 'jwt=$jwtToken',
      },
    );

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      List<Post> postsList = [];
      for (var post in responseData['post_list']) {
        postsList.add(Post.fromJson(post));
      }
      return postsList;
    } else {
      throw Exception('Failed to getFollowingPosts: ${response.body}');
    }
  } catch (error) {
    // ErrorsHandle().displayErrorToast(error, "getFollowingPostss");
    return [];
  }
}
