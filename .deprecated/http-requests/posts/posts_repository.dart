import 'dart:convert';
import 'dart:io';

import 'package:darve/providers/service_provider.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/interfaces/Post.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class PostsRepository {
  Future<List<Post>> getPosts(String username) async {
    try {
      return await ServiceProvider.postsRepository.getPosts(username);
    } catch (error) {
      ErrorsHandle().displayErrorToast(error, "getPosts");
      return [];
    }
  }

  Future<dynamic> createPost(
    String content,
    Future<bool> Function() cb, [
    String filePath = "",
  ]) async {
    try {
      final result = await ServiceProvider.postsRepository.createPost(content, filePath: filePath);
      await cb(); // Call the callback
      return result;
    } catch (error) {
      ErrorsHandle().displayErrorToast(error, "createPost");
      return null;
    }
  }
}
