import 'package:darve/config.dart';
import 'package:darve/mobx/AuthStore/auth_store.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/init.dart';
import 'package:http/http.dart' as http;

Future<dynamic> createPaymentIntent(String amount) async {
  try {
    final AuthStore authStore = Darve.instance.authStore;

    final jwtToken = authStore.jwtToken;
    if (jwtToken == null || jwtToken.isEmpty) {
      throw Exception('createPaymentIntent JWT Token is missing.');
    }

    Uri uri = Uri.parse(
        '${AppConfig.instance.apiUrl}/api/user/wallet/endowment/$amount');

    final response = await http.get(
      uri,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': 'jwt=$jwtToken',
      },
    );

    if (response.statusCode == 200) {
      return response.body;
    } else {
      throw Exception('Failed to createPaymentIntent: ${response.body}');
    }
  } catch (error) {
    ErrorsHandle().displayErrorToast(error, "createPaymentIntent");
  }
}
