import 'dart:convert';
import 'package:darve/config.dart';
import 'package:darve/mobx/AuthStore/auth_store.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/init.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:async';

Stream<dynamic> getBalanceSse() async* {
  try {
    final AuthStore authStore = Darve.instance.authStore;
    final jwtToken = authStore.jwtToken;
    if (jwtToken == null || jwtToken.isEmpty) {
      throw Exception('getBalanceSse JWT Token is missing.');
    }

    final uri = Uri.parse(
      '${AppConfig.instance.apiUrl}/api/user/wallet/balance/sse',
    );

    final request = http.Request('GET', uri);
    request.headers.addAll({
      'Content-Type': 'text/event-stream',
      'Cookie': 'jwt=$jwtToken',
      'Accept': 'application/json',
    });

    final response = await request.send();

    if (response.statusCode != 200) {
      throw Exception('Failed to connect to SSE: ${response.reasonPhrase}');
    }

    final stream = response.stream.transform(utf8.decoder).asBroadcastStream();
    await for (var line in stream) {
      if (line.startsWith('data:')) {
        final jsonData = line.substring(5).trim();
        try {
          // Attempt to decode the JSON data
          yield jsonData;
        } catch (e) {
          // If decoding fails, log the error and send a message back
          debugPrint('Invalid JSON received in SSE data: $jsonData');
          yield null;
        }
      } else {
        // Optionally, yield an error or handle it in a different way
        yield null;
      }
    }
  } catch (error) {
    debugPrint('Error during getBalanceSse: $error');
    // Yield a general error message if an exception occurs
    yield null;
  }
}
