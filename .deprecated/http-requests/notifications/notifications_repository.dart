import 'dart:convert';
import 'package:darve/config.dart';
import 'package:darve/mobx/AuthStore/auth_store.dart';
import 'package:darve/utils/IdExtractor.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/init.dart';
import 'package:darve/utils/interfaces/Notification.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class NotificationsRepository {
  Future<List<DarveNotification>> getNotifications() async {
    List<DarveNotification> notifications = [];
    try {
      final AuthStore authStore = Darve.instance.authStore;

      final jwtToken = authStore.jwtToken;
      if (jwtToken == null || jwtToken.isEmpty) {
        throw Exception('getNotifications JWT Token is missing.');
      }

      final response = await http.get(
        Uri.parse(
            '${AppConfig.instance.apiUrl}/api/notification/user/history'),
        headers: {
          'Accept': 'application/json',
          'Cookie': 'jwt=$jwtToken',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        for (var notif in (responseData as List)) {
          var val = notif['event']['value'];
          String type = notif['event']['type'];

          debugPrint("type ==$type val===$val");

          if (type == DarveNotification.userFollowAdded) {
            val = UserFollowAdded(
                username: val["username"],
                followedUser: val["follows_username"]);
          } else if (type == DarveNotification.userTaskRequestCreated) {
            val = UserTaskRequestCreated(
              taskId: IdExtractor.getIdent(val, propName: "task_id"),
              fromUser: IdExtractor.getIdent(val, propName: "from_user"),
              toUser: IdExtractor.getIdent(val, propName: "to_user"),
            );
          }

          notifications.add(DarveNotification.fromDetails(
              IdExtractor.getIdent(notif), type, val));
        }
        return notifications;
      } else {
        throw Exception(json.decode(response.body)['error']);
      }
    } catch (error) {
      ErrorsHandle().displayErrorToast(error, "getNotifications");
      return notifications;
    }
  }

  Stream<dynamic> getNotificationsSse() async* {
    try {
      final AuthStore authStore = Darve.instance.authStore;
      final jwtToken = authStore.jwtToken;
      if (jwtToken == null || jwtToken.isEmpty) {
        throw Exception('getNotificationsSse JWT Token is missing.');
      }

      final uri = Uri.parse(
        '${AppConfig.instance.apiUrl}/api/notification/user/sse',
      );

      final request = http.Request('GET', uri);
      request.headers.addAll({
        'Content-Type': 'text/event-stream',
        'Cookie': 'jwt=$jwtToken',
        'Accept': 'application/json',
      });

      final response = await request.send();
      if (response.statusCode != 200) {
        throw Exception('Failed to connect to SSE: ${response.reasonPhrase}');
      }

      final stream =
          response.stream.transform(utf8.decoder).asBroadcastStream();
      await for (var line in stream) {
        if (line.startsWith('data:')) {
          final jsonData = line.substring(5).trim();
          try {
            // Attempt to decode the JSON data
            yield jsonData;
          } catch (e) {
            // If decoding fails, log the error and send a message back
            debugPrint('Invalid JSON received in SSE data: $jsonData');
            yield null;
          }
        } else {
          // Optionally, yield an error or handle it in a different way
          yield null;
        }
      }
    } catch (error) {
      debugPrint('Error during getNotificationsSse: $error');
      // Yield a general error message if an exception occurs
      yield null;
    }
  }
}
