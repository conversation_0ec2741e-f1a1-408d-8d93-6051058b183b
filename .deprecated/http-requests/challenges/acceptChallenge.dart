import 'dart:convert';
import 'package:darve/config.dart';
import 'package:darve/mobx/AuthStore/auth_store.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/init.dart';
import 'package:http/http.dart' as http;

Future<dynamic> acceptChallenge(String taskId, bool accepted) async {
  try {
    final AuthStore authStore = Darve.instance.authStore;

    final jwtToken = authStore.jwtToken;
    if (jwtToken == null || jwtToken.isEmpty) {
      throw Exception('followUser JWT Token is missing.');
    }

    Uri uri = Uri.parse(
        '${AppConfig.instance.apiUrl}/api/task_request/$taskId/accept');

    final response = await http.post(
      uri,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': 'jwt=$jwtToken',
      },
      body: jsonEncode({
        "accept": accepted,
      }),
    );

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      return responseData;
    } else {
      throw Exception('Failed to acceptChallenge: ${response.body}');
    }
  } catch (error) {
    ErrorsHandle().displayErrorToast(error, "acceptChallenge");
  }
}
