import 'dart:convert';
import 'package:darve/config.dart';
import 'package:darve/mobx/AuthStore/auth_store.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/init.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

Future<dynamic> getAllReceivedTaskRequestsForPost() async {
  try {
    final AuthStore authStore = Darve.instance.authStore;

    final jwtToken = authStore.jwtToken;
    if (jwtToken == null || jwtToken.isEmpty) {
      throw Exception('JWT Token is missing.');
    }

    final response = await http.get(
      Uri.parse('${AppConfig.instance.apiUrl}/api/task_request/received'),
      headers: {
        'Accept': 'application/json',
        'Cookie': 'jwt=$jwtToken',
      },
    );

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      debugPrint(responseData.toString());
      return responseData;
    } else {
      throw Exception(
          'Failed to fetch received task requests: ${response.body}');
    }
  } catch (error) {
    ErrorsHandle()
        .displayErrorToast(error, "getAllReceivedTaskRequestsForPost");
  }
}
