// import 'dart:convert';
// import 'dart:math';
// import 'package:darve/config.dart';
// import 'package:darve/mobx/AuthStore/auth_store.dart';
// import 'package:darve/providers/service_provider.dart';
// import 'package:darve/utils/constants.dart';
// import 'package:darve/utils/errors.dart';
// import 'package:darve/utils/init.dart';
// import 'package:flutter/material.dart';
// import 'package:http/http.dart' as http;
//
// class ChatRepository {
//   Future<dynamic> getChatsList() async {
//     try {
//       return await ServiceProvider.chatRepository.getChatsList();
//     } catch (error) {
//       ErrorsHandle().displayErrorToast(error, "getChatsList");
//       return null;
//     }
//   }
//
//   Stream<dynamic> getChatListSse() async* {
//     var emittedCount = 0;
//     while (true) {
//       emittedCount++;
//       try {
//         final AuthStore authStore = Darve.instance.authStore;
//         final jwtToken = authStore.jwtToken;
//         if (jwtToken == null || jwtToken.isEmpty) {
//           throw Exception('getChatListSse JWT Token is missing.');
//         }
//
//         final uri = Uri.parse(
//           '${AppConfig.instance.apiUrl}/api/events/sse',
//         );
//
//         final request = http.Request('GET', uri);
//         request.headers.addAll({
//           'Content-Type': 'text/event-stream',
//           'Accept': 'application/json',
//           'Cookie': 'jwt=$jwtToken',
//         });
//
//         final response = await request.send();
//         if (response.statusCode != 200) {
//           throw Exception('Failed to connect to SSE: ${response.reasonPhrase}');
//         }
//
//         final stream = response.stream.transform(utf8.decoder);
//
//         // Listen to the stream and yield data
//         await for (var line in stream) {
//           if (line.startsWith('data:')) {
//             final jsonData = line.substring(5).trim();
//             try {
//               yield jsonData; // Emit parsed JSON
//             } catch (e) {
//               debugPrint('Invalid JSON received in SSE data: $jsonData');
//               yield null; // Optionally yield null for invalid data
//             }
//           }
//         }
//       } catch (error) {
//         debugPrint(
//             'emittedCount: $emittedCount Error during getChatListSse: $error');
//         yield {error: error, emittedCount: emittedCount};
//
//         // Wait a bit before retrying to prevent rapid reconnections
//         await Future.delayed(const Duration(seconds: 1));
//       }
//     }
//   }
//
//   Future<List<Map<String, dynamic>>> getChatDiscussionById(
//       String discussionId) async {
//     try {
//       final AuthStore authStore = Darve.instance.authStore;
//
//       final jwtToken = authStore.jwtToken;
//       if (jwtToken == null || jwtToken.isEmpty) {
//         throw Exception('getChatDiscussionById JWT Token is missing.');
//       }
//
//       Uri url =
//           Uri.parse('${AppConfig.instance.apiUrl}/discussion/$discussionId');
//       final response = await http.get(
//         url,
//         headers: {
//           'Accept': 'application/json',
//           'Cookie': 'jwt=$jwtToken',
//         },
//       );
//
//       if (response.statusCode == 200) {
//         final responseData = json.decode(response.body);
//         List<Map<String, dynamic>> posts = (responseData['posts'] as List)
//             .map((item) => item as Map<String, dynamic>)
//             .toList();
//         return posts.reversed.toList();
//       } else {
//         throw Exception(
//             'Failed to getChatDiscussionById: ${response.body} for $discussionId');
//       }
//     } catch (error) {
//       ErrorsHandle().displayErrorToast(error, "getChatDiscussionById");
//       return [];
//     }
//   }
//
//   Stream<dynamic> getChatDiscussionByIdSse(String discussionId) async* {
//     try {
//       final AuthStore authStore = Darve.instance.authStore;
//       final jwtToken = authStore.jwtToken;
//       if (jwtToken == null || jwtToken.isEmpty) {
//         throw Exception('getChatDiscussionByIdSse JWT Token is missing.');
//       }
//
//       final uri = Uri.parse(
//         '${AppConfig.instance.apiUrl}/api/discussion/$discussionId/sse',
//       );
//
//       final request = http.Request('GET', uri);
//       request.headers.addAll({
//         'Content-Type': 'text/event-stream',
//         'Cookie': 'jwt=$jwtToken',
//         'Accept': 'application/json',
//       });
//
//       final response = await request.send();
//       if (response.statusCode != 200) {
//         throw Exception('Failed to connect to SSE: ${response.reasonPhrase}');
//       }
//
//       final stream =
//           response.stream.transform(utf8.decoder).asBroadcastStream();
//       await for (var line in stream) {
//         if (line.startsWith('data:')) {
//           final jsonData = line.substring(5).trim();
//           try {
//             // Attempt to decode the JSON data
//             yield jsonData;
//           } catch (e) {
//             // If decoding fails, log the error and send a message back
//             debugPrint('Invalid JSON received in SSE data: $jsonData');
//             yield null;
//           }
//         } else {
//           // Optionally, yield an error or handle it in a different way
//           yield null;
//         }
//       }
//     } catch (error) {
//       debugPrint('Error during getChatDiscussionByIdSse: $error');
//       // Yield a general error message if an exception occurs
//       yield null;
//     }
//   }
//
//   Future<dynamic> createChatWithUserId(String otherUserId) async {
//     try {
//       final AuthStore authStore = Darve.instance.authStore;
//
//       final jwtToken = authStore.jwtToken;
//       if (jwtToken == null || jwtToken.isEmpty) {
//         throw Exception('createChatWithUserId JWT Token is missing.');
//       }
//
//       final response = await http.get(
//         Uri.parse(
//             '${AppConfig.instance.apiUrl}/api/user_chat/with/$otherUserId'),
//         headers: {
//           'Accept': 'application/json',
//           'Cookie': 'jwt=$jwtToken',
//         },
//       );
//
//       if (response.statusCode == 200) {
//         final responseData = json.decode(response.body);
//         debugPrint(responseData.toString());
//         return responseData;
//       } else {
//         throw Exception('Failed to createChatWithUserId: ${response.body}');
//       }
//     } catch (error) {
//       ErrorsHandle().displayErrorToast(error, "createChatWithUserId");
//     }
//   }
//
//   String generateRandomTitle(int length) {
//     const characters =
//         'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*()_-+=<>?';
//     Random random = Random();
//
//     return List.generate(
//             length, (index) => characters[random.nextInt(characters.length)])
//         .join();
//   }
//
//   Future<dynamic> postMessageInChat(String discussionId, String content) async {
//     try {
//       final AuthStore authStore = Darve.instance.authStore;
//
//       final jwtToken = authStore.jwtToken;
//       if (jwtToken == null || jwtToken.isEmpty) {
//         throw Exception('JWT Token is missing.');
//       }
//
//       final postTitle = generateRandomTitle(32);
//
//       final uri = Uri.parse(
//           '${AppConfig.instance.apiUrl}/api/discussion/$discussionId/post');
//       final request = http.MultipartRequest('POST', uri)
//         ..headers['Cookie'] = 'jwt=$jwtToken'
//         ..headers['Accept'] = 'application/json'
//         ..fields['title'] = postTitle
//         ..fields['content'] = content
//         ..fields['topic_id'] = "";
//
//       final response = await request.send();
//
//       if (response.statusCode == 200 || response.statusCode == 201) {
//         final responseData = await response.stream.bytesToString();
//         debugPrint(responseData.toString());
//         return responseData;
//       } else {
//         final errorData = await response.stream.bytesToString();
//         throw Exception('Failed to post message: $errorData');
//       }
//     } catch (error) {
//       ErrorsHandle().displayErrorToast(error, "postMessageInChat");
//       return null;
//     }
//   }
// }
