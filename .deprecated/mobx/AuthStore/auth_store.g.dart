// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$AuthStore on _AuthStore, Store {
  late final _$jwtTokenAtom =
      Atom(name: '_AuthStore.jwtToken', context: context);

  @override
  String? get jwtToken {
    _$jwtTokenAtom.reportRead();
    return super.jwtToken;
  }

  @override
  set jwtToken(String? value) {
    _$jwtTokenAtom.reportWrite(value, super.jwtToken, () {
      super.jwtToken = value;
    });
  }

  late final _$isLoggedInAtom =
      Atom(name: '_AuthStore.isLoggedIn', context: context);

  @override
  bool get isLoggedIn {
    _$isLoggedInAtom.reportRead();
    return super.isLoggedIn;
  }

  @override
  set isLoggedIn(bool value) {
    _$isLoggedInAtom.reportWrite(value, super.isLoggedIn, () {
      super.isLoggedIn = value;
    });
  }

  late final _$loadJWTAsyncAction =
      AsyncAction('_AuthStore.loadJWT', context: context);

  @override
  Future<void> loadJWT() {
    return _$loadJWTAsyncAction.run(() => super.loadJWT());
  }

  late final _$setJWTAsyncAction =
      AsyncAction('_AuthStore.setJWT', context: context);

  @override
  Future<void> setJWT(String token) {
    return _$setJWTAsyncAction.run(() => super.setJWT(token));
  }

  late final _$logoutAsyncAction =
      AsyncAction('_AuthStore.logout', context: context);

  @override
  Future<void> logout() {
    return _$logoutAsyncAction.run(() => super.logout());
  }

  @override
  String toString() {
    return '''
jwtToken: ${jwtToken},
isLoggedIn: ${isLoggedIn}
    ''';
  }
}
