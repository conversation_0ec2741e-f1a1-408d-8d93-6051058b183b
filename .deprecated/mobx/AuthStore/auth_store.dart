import 'package:flutter/material.dart';
import 'package:mobx/mobx.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'auth_store.g.dart';

class AuthStore = _AuthStore with _$AuthStore;

abstract class _AuthStore with Store {
  // Store for JWT token
  @observable
  String? jwtToken;

  // Store for the login status
  @observable
  bool isLoggedIn = false;

  // Load the JWT token from SharedPreferences on startup
  @action
  Future<void> loadJWT() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    jwtToken = prefs.getString('jwt_token');
    isLoggedIn = jwtToken != null;
  }

  // Set the JWT token and update the login status
  @action
  Future<void> setJWT(String token) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('jwt_token', token);
    jwtToken = token;
    isLoggedIn = true;
  }

  // Remove the JWT token and update the login status
  @action
  Future<void> logout() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('jwt_token');
    jwtToken = null;
    isLoggedIn = false;
    debugPrint("logging out the user");
  }
}
