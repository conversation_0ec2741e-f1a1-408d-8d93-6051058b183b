// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$UserStore on _UserStore, Store {
  late final _$usernameAtom =
      Atom(name: '_UserStore.username', context: context);

  @override
  String? get username {
    _$usernameAtom.reportRead();
    return super.username;
  }

  @override
  set username(String? value) {
    _$usernameAtom.reportWrite(value, super.username, () {
      super.username = value;
    });
  }

  late final _$userIdAtom = Atom(name: '_UserStore.userId', context: context);

  @override
  String? get userId {
    _$userIdAtom.reportRead();
    return super.userId;
  }

  @override
  set userId(String? value) {
    _$userIdAtom.reportWrite(value, super.userId, () {
      super.userId = value;
    });
  }

  late final _$image_uriAtom =
      Atom(name: '_UserStore.image_uri', context: context);

  @override
  String? get image_uri {
    _$image_uriAtom.reportRead();
    return super.image_uri;
  }

  @override
  set image_uri(String? value) {
    _$image_uriAtom.reportWrite(value, super.image_uri, () {
      super.image_uri = value;
    });
  }

  late final _$full_nameAtom =
      Atom(name: '_UserStore.full_name', context: context);

  @override
  String? get full_name {
    _$full_nameAtom.reportRead();
    return super.full_name;
  }

  @override
  set full_name(String? value) {
    _$full_nameAtom.reportWrite(value, super.full_name, () {
      super.full_name = value;
    });
  }

  late final _$bioAtom = Atom(name: '_UserStore.bio', context: context);

  @override
  String? get bio {
    _$bioAtom.reportRead();
    return super.bio;
  }

  @override
  set bio(String? value) {
    _$bioAtom.reportWrite(value, super.bio, () {
      super.bio = value;
    });
  }

  late final _$emailAtom = Atom(name: '_UserStore.email', context: context);

  @override
  String? get email {
    _$emailAtom.reportRead();
    return super.email;
  }

  @override
  set email(String? value) {
    _$emailAtom.reportWrite(value, super.email, () {
      super.email = value;
    });
  }

  late final _$loadUserStoreAsyncAction =
      AsyncAction('_UserStore.loadUserStore', context: context);

  @override
  Future<void> loadUserStore() {
    return _$loadUserStoreAsyncAction.run(() => super.loadUserStore());
  }

  late final _$setUserStoreAsyncAction =
      AsyncAction('_UserStore.setUserStore', context: context);

  @override
  Future<void> setUserStore(String _username, String _userId, String _image_uri,
      String _full_name, String _bio, String _email) {
    return _$setUserStoreAsyncAction.run(() => super.setUserStore(
        _username, _userId, _image_uri, _full_name, _bio, _email));
  }

  late final _$cleanAsyncAction =
      AsyncAction('_UserStore.clean', context: context);

  @override
  Future<void> clean() {
    return _$cleanAsyncAction.run(() => super.clean());
  }

  @override
  String toString() {
    return '''
username: ${username},
userId: ${userId},
image_uri: ${image_uri},
full_name: ${full_name},
bio: ${bio},
email: ${email}
    ''';
  }
}
