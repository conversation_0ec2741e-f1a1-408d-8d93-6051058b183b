import 'package:mobx/mobx.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'user_store.g.dart';

class UserStore = _UserStore with _$UserStore;

abstract class _UserStore with Store {
  @observable
  String? username;

  @observable
  String? userId;

  @observable
  String? image_uri;

  @observable
  String? full_name;

  @observable
  String? bio;

  @observable
  String? email;

  @action
  Future<void> loadUserStore() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    username = prefs.getString('username');
    userId = prefs.getString('userId');
    image_uri = prefs.getString('image_uri');
    full_name = prefs.getString('full_name');
    bio = prefs.getString('bio');
    email = prefs.getString('email');
  }

  @action
  Future<void> setUserStore(
    String _username,
    String _userId,
    String _image_uri,
    String _full_name,
    String _bio,
    String _email
    ) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('username', _username);
    await prefs.setString('userId', _userId);
    await prefs.setString('image_uri', _image_uri);
    await prefs.setString('full_name', _full_name);
    await prefs.setString('bio', _bio);
    await prefs.setString('email', _email);
    userId = _userId;
    username = _username;
    image_uri = _image_uri;
    full_name = _full_name;
    bio = _bio;
    email = _email;
  }

  @action
  Future<void> clean() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('username');
    await prefs.remove('userId');
    await prefs.remove('image_uri');
    await prefs.remove('full_name');
    await prefs.remove('bio');
    await prefs.remove('email');
    username = null;
    userId=null;
    image_uri=null;
    full_name=null;
    bio=null;
    email=null;
  }
}
