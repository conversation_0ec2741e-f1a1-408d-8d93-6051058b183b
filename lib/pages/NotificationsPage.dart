import 'package:darve/api/models/user_notification_model.dart';
import 'package:darve/components/common/showSnackbar.dart';
import 'package:darve/controllers/chat_list_controller.dart';
import 'package:darve/controllers/notifications_controller.dart';
import 'package:darve/routes/route_helper.dart';
import 'package:darve/utils/http-requests/profile/profile_repository.dart';
import 'package:darve/utils/interfaces/chat_storage.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  final ChatListController chatController = Get.put(ChatListController());
  final NotificationsController notificationsController =
      Get.put(NotificationsController());

  Future<List<Widget>> buildList() async {
    List<Widget> widgetsList = [];
    int chatListIdx = 0;
    await chatController.getChats();
    List<ChatList> chatList = chatController.chatList;

    for (var i = 0; i < notificationsController.notifications.length; i++) {
      var notif = notificationsController.notifications[i];

      if (notif.event == UserNotificationEvent.userFollowAdded) {
        // Extract username from metadata
        String username = notif.metadata?['username'] ?? notif.createdBy;
        final details = await ProfileRepository().getProfileData(username);
        var isFollowingUser =
            await ProfileRepository().isFollowing(details.userId!);
        widgetsList.add(buildFollowActivityTile(username, details.userId!,
            imageUri: details.imageUri!, isFollowing: isFollowingUser));
      }
      if (notif.event == UserNotificationEvent.userChatMessage) {
        // Extract chat info from metadata
        String chatId = notif.metadata?['chatId'] ?? "";
        String title = notif.metadata?['title'] ?? notif.title;
        String imageUri = notif.metadata?['imageUri'] ?? "";
        String userId = notif.metadata?['userId'] ?? notif.createdBy;

        // If metadata doesn't have chat info, try to find it in chat list
        if (chatId.isEmpty && chatList.isNotEmpty && chatListIdx < chatList.length) {
          var chat = chatList[chatListIdx];
          chatId = chat.id;
          title = chat.title ?? "";
          imageUri = chat.avatarUrl ?? "";
          userId = chat.userIds ?? "";
          chatListIdx++;
        }

        if (chatId.isNotEmpty) {
          widgetsList.add(buildReceivedMessage(title, "New message", chatId, title, userId, imageUri: imageUri));
        }
      }

      if (notif.event == UserNotificationEvent.userTaskRequestReceived) {
        widgetsList.add(buildGenericNotificationTile(notif));
      }

      if (notif.event == UserNotificationEvent.userTaskRequestDelivered) {
        widgetsList.add(buildGenericNotificationTile(notif));
      }

      if (notif.event == UserNotificationEvent.userCommunityPost) {
        widgetsList.add(buildGenericNotificationTile(notif));
      }

      if (notif.event == UserNotificationEvent.userTaskRequestCreated) {
        widgetsList.add(buildGenericNotificationTile(notif));
      }

      if (notif.event == UserNotificationEvent.userBalanceUpdate) {
        widgetsList.add(buildGenericNotificationTile(notif));
      }

      if (notif.event == UserNotificationEvent.userLikePost) {
        widgetsList.add(buildGenericNotificationTile(notif));
      }
    }
    return widgetsList.isNotEmpty
        ? widgetsList
        : [
            Container(
              color: Colors.white,
              child: const Center(
                child: Text(
                  "No notifications yet!",
                  style: Styles.lightTextStyle,
                ),
              ),
            )
          ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Notifications',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.black,
            fontSize: 22,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => RouteHelper.goBack(),
        ),
      ),
      body: Container(
        color: Colors.white,
        padding: const EdgeInsets.all(16),
        child: Obx(
          () => notificationsController.notifications.isEmpty
              ? const Center(
                  child: CircularProgressIndicator(
                    color: Styles.primaryColor,
                  ),
                )
              : FutureBuilder<List<Widget>>(
                  future: buildList(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState != ConnectionState.done) {
                      return const Center(
                        child: CircularProgressIndicator(
                          color: Styles.primaryColor,
                        ),
                      );
                    }
                    if (snapshot.hasError) {
                      return Center(
                        child: Text(
                          'Error loading notifications ${snapshot.error}',
                          style: Styles.lightTextStyle,
                        ),
                      );
                    }
                    return Column(
                      children: snapshot.data ?? [],
                    );
                  },
                ),
        ),
      ),
    );
  }

  Widget buildFollowActivityTile(String username, String userId,
      {bool isFollowing = false, required String imageUri}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              RouteHelper.goToProfile(
                userId: userId,
                username: username,
                imageUrl: imageUri,
                fullName: "",
              );
            },
            child: CircleAvatar(
              radius: 24,
              backgroundImage: NetworkImage(imageUri),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text.rich(
              TextSpan(
                text: '@$username',
                style: const TextStyle(fontWeight: FontWeight.bold),
                children: const [
                  TextSpan(
                    text: ' started following you.',
                    style: TextStyle(fontWeight: FontWeight.normal),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          isFollowing
              ? _blackButton("Following", onClick: () async {
                  await ProfileRepository().unfollowUser(userId);
                  SnackbarHelper.showFollowSnackbar(
                      context: context,
                      imageUri: imageUri,
                      username: username,
                      bgColor: Colors.red,
                      isFollowed: false);
                  setState(() {});
                })
              : _outlineButton("Follow", onClick: () async {
                  await ProfileRepository().followUser(userId);
                  SnackbarHelper.showFollowSnackbar(
                      context: context,
                      imageUri: imageUri,
                      username: username,
                      bgColor: Colors.green,
                      isFollowed: true);
                  setState(() {});
                }),
        ],
      ),
    );
  }

  Widget buildReceivedMessage(String username, String message, String chatId,
      String title, String userId,
      {required String imageUri}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              RouteHelper.goToProfile(
                userId: userId,
                username: username,
                imageUrl: imageUri,
                fullName: "",
              );
            },
            child: CircleAvatar(
              radius: 24,
              backgroundImage: NetworkImage(imageUri),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text.rich(
              TextSpan(
                text: "@$username",
                style: const TextStyle(fontWeight: FontWeight.bold),
                children: [
                  const TextSpan(
                    text: ' sent a message ',
                    style: TextStyle(fontWeight: FontWeight.normal),
                  ),
                  TextSpan(
                    text: " '$message' ",
                    style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontStyle: FontStyle.italic,
                        fontSize: 12),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          _blackButton("Reply", onClick: () {
            RouteHelper.goToChat(
              chatId: chatId,
              title: title,
              avatarUrl: imageUri,
              userId: userId,
            );
          }),
        ],
      ),
    );
  }

  Widget _blackButton(String text, {Function? onClick}) {
    return GestureDetector(
      onTap: () {
        if (onClick != null) {
          onClick();
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(text, style: const TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget _outlineButton(String text, {Function? onClick}) {
    return GestureDetector(
      onTap: () {
        if (onClick != null) {
          onClick();
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.black),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(text, style: const TextStyle(color: Colors.black)),
      ),
    );
  }

  Widget buildGenericNotificationTile(UserNotificationModel notification) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notification.isRead ? Colors.grey[100] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: Styles.primaryColor,
            child: Icon(
              _getIconForEvent(notification.event),
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  notification.title,
                  style: TextStyle(
                    fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _getEventDescription(notification.event),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          if (!notification.isRead)
            GestureDetector(
              onTap: () => notificationsController.markAsRead(notification.id),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Styles.primaryColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  'Mark Read',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
              ),
            ),
        ],
      ),
    );
  }

  IconData _getIconForEvent(UserNotificationEvent event) {
    switch (event) {
      case UserNotificationEvent.userBalanceUpdate:
        return Icons.account_balance_wallet;
      case UserNotificationEvent.userTaskRequestCreated:
      case UserNotificationEvent.userTaskRequestReceived:
      case UserNotificationEvent.userTaskRequestDelivered:
        return Icons.task;
      case UserNotificationEvent.userCommunityPost:
        return Icons.post_add;
      case UserNotificationEvent.userLikePost:
        return Icons.favorite;
      default:
        return Icons.notifications;
    }
  }

  String _getEventDescription(UserNotificationEvent event) {
    switch (event) {
      case UserNotificationEvent.userBalanceUpdate:
        return 'Your balance has been updated';
      case UserNotificationEvent.userTaskRequestCreated:
        return 'A new task request was created';
      case UserNotificationEvent.userTaskRequestReceived:
        return 'You received a task request';
      case UserNotificationEvent.userTaskRequestDelivered:
        return 'A task request was delivered';
      case UserNotificationEvent.userCommunityPost:
        return 'New community post';
      case UserNotificationEvent.userLikePost:
        return 'Someone liked your post';
      default:
        return 'New notification';
    }
  }
}
