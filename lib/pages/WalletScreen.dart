import 'dart:async';

import 'package:darve/components/WalletPage/PortfolioCard.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/helpers/discussion_helper.dart';
import 'package:darve/utils/http-requests/request_cache.dart';

import 'package:darve/components/WalletPage/SkeletonLoader.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:darve/utils/interfaces/Transaction.dart';
import 'package:darve/helpers/user_helper.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';

class WalletScreen extends StatefulWidget {
  const WalletScreen({super.key});

  @override
  State<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen> {
  String balance = "\$0";
  String percentageIncrease = "12.42";
  List<Transaction> transactions = [];
  bool isLoadingCache = false;
  bool isLoadingTransactions = false;
  int start = 0;
  int count = 5;
  ScrollController scrollController = ScrollController();
  StreamSubscription? _balanceSubscription;
  late RequestCache requestCache;

  @override
  void initState() {
    super.initState();
    _balanceSubscription = ServiceProvider.walletRepository.getBalanceSse().listen((val) {
      if (val == "event: UserBalanceUpdate") {
        fetchTransactions(isReset: true);
        refreshBalance();
      }
    });
    loadData();
    scrollController.addListener(_scrollListener);
  }

  void loadData() async {
    requestCache = await RequestCache.getInstance();
    await fetchTransactionsCache();
    await fetchTransactions();
    await refreshBalance();
  }

  @override
  void dispose() {
    scrollController.dispose();
    _balanceSubscription!.cancel();
    super.dispose();
  }

  Future<void> refreshBalance() async {
    var _balance = await ServiceProvider.walletRepository.getBalance();
    if (mounted) {
      setState(() {
        balance = "\$${_balance['balance']['balance_usd']}";
      });
    }
  }

  Future<void> fetchTransactionsCache({isReset = false}) async {
    if (isReset) {
      setState(() {
        transactions = [];
        start = 0;
      });
    }

    if (isLoadingCache) return;
    setState(() {
      isLoadingCache = true;
    });

    if (transactions.isNotEmpty) {
      setState(() {
        isLoadingCache = false;
      });
      return;
    }

    try {
      var transactionsCache = requestCache.getWalletHistoryCache();
      if (transactionsCache == null) {
        return;
      }
      List<Transaction> parsedTransactions = [];
      for (var transaction in transactionsCache["transactions"]) {
        String otherUserId =
            UserIdHelper().getUserId(transaction["with_wallet"]);

        bool isOtherUserGateway =
            otherUserId == UserIdHelper.APP_GATEWAY_WALLET;

        String type =
            transaction["amount_out"] == null ? "Deposit" : "Withdraw";
        int amount = transaction["amount_in"] ?? transaction["amount_out"];
        String username = transaction['wallet']['user']['username'] ?? "";

        DateTime created = DateTime.parse(transaction["r_created"]);

        parsedTransactions.add(Transaction.fromDetails(
          DiscussionIdHelper().getDiscussionId(transaction),
          DiscussionIdHelper().getDiscussionId(transaction["wallet"]),
          type,
          amount,
          isOtherUserGateway,
          type == WalletType.deposit.value
              ? isOtherUserGateway
                  ? "Deposited in Darve Wallet by $username"
                  : "Received from $otherUserId in $username"
              : "Sent to $otherUserId by $username",
          created,
        ));
        parsedTransactions.sort((a, b) => a.created.compareTo(b.created));
      }

      if (mounted) {
        setState(() {
          transactions.addAll(parsedTransactions.reversed);
        });
      }
    } catch (error) {
      debugPrint('Error fetching transactions: $error');
    } finally {
      if (mounted) {
        setState(() {
          isLoadingCache = false;
        });
      }
    }
  }

  Future<void> fetchTransactions({isReset = false}) async {
    if (isReset) {
      setState(() {
        transactions = [];
        start = 0;
      });
    }

    if (isLoadingTransactions) return;
    setState(() {
      isLoadingTransactions = true;
    });

    await Future.delayed(const Duration(seconds: 1));

    try {
      var res = await ServiceProvider.walletRepository.getUserTxHistory(count: count, start: start);

      if (start == 0) {
        transactions.clear();
      }

      List<Transaction> parsedTransactions = [];

      requestCache.setWalletHistoryCache(res);

      for (var transaction in res["transactions"]) {
        String otherUserId =
            UserIdHelper().getUserId(transaction["with_wallet"]);

        bool isOtherUserGateway =
            otherUserId == UserIdHelper.APP_GATEWAY_WALLET;

        String type =
            transaction["amount_out"] == null ? "Deposit" : "Withdraw";
        int amount = transaction["amount_in"] ?? transaction["amount_out"];
        String username = transaction['wallet']['user']['username'] ?? "";

        DateTime created = DateTime.parse(transaction["r_created"]);

        parsedTransactions.add(Transaction.fromDetails(
          DiscussionIdHelper().getDiscussionId(transaction),
          DiscussionIdHelper().getDiscussionId(transaction["wallet"]),
          type,
          amount,
          isOtherUserGateway,
          type == WalletType.deposit.value
              ? isOtherUserGateway
                  ? "Deposited in Darve Wallet by $username"
                  : "Received from $otherUserId in $username"
              : "Sent to $otherUserId by $username",
          created,
        ));
        parsedTransactions.sort((a, b) => a.created.compareTo(b.created));
      }

      if (mounted) {
        setState(() {
          transactions.addAll(parsedTransactions.reversed);
          start += count;
        });
      }
    } catch (error) {
      debugPrint('Error fetching transactions: $error');
    } finally {
      if (mounted) {
        setState(() {
          isLoadingTransactions = false;
        });
      }
    }
  }

  IconData getTransactionIcon(String type, bool isOtherUserGateway) {
    return type == WalletType.deposit.value
        ? !isOtherUserGateway
            ? FontAwesomeIcons.plus
            : FontAwesomeIcons.arrowDown
        : !isOtherUserGateway
            ? FontAwesomeIcons.minus
            : FontAwesomeIcons.arrowUp;
  }

  void _scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent) {
      fetchTransactions();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text("Wallet",
            style: TextStyle(fontWeight: FontWeight.w400, fontSize: 20)),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            isLoadingCache
                ? const Center(child: CircularProgressIndicator())
                : PortfolioCard(
                    balance: balance, percentageIncrease: percentageIncrease),
            const SizedBox(height: 30),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                const Text("Transaction History",
                    style:
                        TextStyle(fontSize: 16, fontWeight: FontWeight.w400)),
                const Spacer(),
                if (isLoadingTransactions)
                  const SizedBox(
                      height: 16,
                      width: 16,
                      child: CircularProgressIndicator()),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                controller: scrollController,
                itemCount: transactions.length + (isLoadingCache ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == transactions.length && isLoadingCache) {
                    return const Column(
                      children: [
                        SkeletonLoaderCard(),
                        SizedBox(height: 4),
                        SkeletonLoaderCard(),
                        SizedBox(height: 4),
                        SkeletonLoaderCard(),
                        SizedBox(height: 4),
                        SkeletonLoaderCard(),
                        SizedBox(height: 4)
                      ],
                    );
                  }

                  final transaction = transactions[index];
                  bool isDeposit = transaction.type == WalletType.deposit.value;
                  return Card(
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                    color: Colors.white,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: ListTile(
                        leading: Stack(
                          clipBehavior: Clip.none,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12.0),
                              decoration: BoxDecoration(
                                color: isDeposit
                                    ? Colors.green.shade50
                                    : Colors.red.shade50,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                getTransactionIcon(transaction.type,
                                    transaction.isOtherUserGateway),
                                color: isDeposit ? Colors.green : Colors.red,
                                size: 18,
                              ),
                            ),
                            if (!isDeposit)
                              Positioned(
                                bottom: -4,
                                right: -4,
                                child: Icon(
                                  FontAwesomeIcons.lock,
                                  size: 12,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                          ],
                        ),
                        title: Text(
                          transaction.type,
                          style: const TextStyle(
                              fontWeight: FontWeight.w500, fontSize: 14),
                        ),
                        trailing: Padding(
                          padding: const EdgeInsets.only(top: 6.0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                "\$${transaction.amount}",
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  color: isDeposit ? Colors.green : Colors.red,
                                  fontSize: 14,
                                ),
                              ),
                              Text(
                                DateFormat('yyyy-MM-dd HH:mm:ss')
                                    .format(transaction.created),
                                style: const TextStyle(
                                    fontSize: 10,
                                    color: Colors.grey,
                                    fontWeight: FontWeight.w400),
                                textAlign: TextAlign.right,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
