import 'package:darve/components/SearchPage/RecentAvatar.dart';
import 'package:darve/components/common/search_field.dart';
import 'package:darve/mobx/UserStore/user_store.dart';
import 'package:darve/pages/profile_page.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:darve/utils/init.dart';
import 'package:darve/utils/interfaces/ProfileData.dart';
import 'package:darve/utils/serverAssets.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  String searchText = '';
  bool noResults = true;
  String? searchedUsername;
  ProfileData user = ProfileData.empty();
  final UserStore _userStore = Darve.instance.userStore;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.white,
        title: const Text(
          'Search',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.black,
            fontSize: 22,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.only(bottom: 30),
        child: Container(
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Search bar
              SearchField(
                onChanged: (value) async {
                  setState(() {
                    searchText = value;
                  });
                  if (value.length >= 3) {
                    var res = await ServiceProvider.profileRepository.searchUser(value);
                    if (res.isNotEmpty) {
                      var userDetails = await ServiceProvider.profileRepository
                          .getProfileData(res[0]['username']);
                      setState(() {
                        searchedUsername = res[0]['username'];
                        user = userDetails;
                        noResults = false;
                      });
                    } else {
                      setState(() {
                        noResults = true;
                      });
                    }
                  }
                },
              ),

              if (noResults && searchText.isNotEmpty)
                Center(
                  child: Text(
                    "No user with username @$searchText",
                    style: const TextStyle(
                      fontWeight: FontWeight.w400,
                      color: Styles.textLightColor,
                    ),
                  ),
                ),

              if (!noResults && searchText.isNotEmpty)
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () async {
                    final details = await ServiceProvider.profileRepository
                        .getProfileData(searchedUsername!);
                    if (_userStore.username != searchText) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => Scaffold(
                            body: ProfilePage(
                              details.userId!,
                              details.userName!,
                              details.imageUri!,
                              details.fullName!,
                            ),
                          ),
                        ),
                      );
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 10,
                      horizontal: 18,
                    ),
                    child: Row(
                      children: [
                        CircleAvatar(
                          backgroundImage: NetworkImage(
                            ServerAssets().getAssetUrl(user.imageUri!),
                          ),
                          radius: 24,
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    user.fullName!,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    _userStore.username == searchText
                                        ? ' (YOU)'
                                        : '',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: Styles.textLightColor,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 2),
                              Text(
                                user.userName!,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              if (searchText.isEmpty) getDefaultSearchScreen(context),
            ],
          ),
        ),
      ),
    );
  }
}

Widget getDefaultSearchScreen(BuildContext context) {
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 30.0, vertical: 10.0),
    child: Column(
      children: [
        const Row(
          children: [
            Text(
              'Recent',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.black,
                fontSize: 18,
                letterSpacing: -0.4,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8.0),
        const SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              RecentAvatar(),
              RecentAvatar(),
              RecentAvatar(),
              RecentAvatar(),
              RecentAvatar(),
            ],
          ),
        ),
        const SizedBox(height: 20.0),
        const Row(
          children: [
            Text(
              'Search by Category',
              style: TextStyle(
                fontWeight: FontWeight.w700,
                color: Colors.black,
                fontSize: 18,
                letterSpacing: -0.4,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8.0),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                categoryTile(context, "Gym", 0.50,
                    "https://i.pinimg.com/736x/d1/3f/21/d13f219a54c0011a79d724668ba07024.jpg"),
                categoryTile(context, "Work", 0.65,
                    "https://i.pinimg.com/736x/19/77/52/1977525a540ac8bdae0b5ef2ac14d126.jpg"),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                categoryTile(context, "Cooking", 0.65,
                    "https://i.pinimg.com/736x/ac/dd/e3/acdde3f104e4bc16e98f0e2ab6551971.jpg"),
                categoryTile(context, "Sport", 0.50,
                    "https://i.pinimg.com/736x/eb/b9/a7/ebb9a75cd87a6fa56342ff2ea683b5d0.jpg"),
              ],
            ),
          ],
        ),
      ],
    ),
  );
}

Widget categoryTile(
    BuildContext context, String label, double ratio, String imageUrl) {
  return InkWell(
    onTap: () {
      // Handle tap
    },
    child: Container(
      height: MediaQuery.of(context).size.width * ratio,
      width: MediaQuery.of(context).size.width * 0.41,
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.black,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: .2),
            offset: const Offset(0, 2),
            blurRadius: 6,
          ),
        ],
        image: DecorationImage(
          opacity: 0.6,
          image: NetworkImage(imageUrl),
          fit: BoxFit.cover,
        ),
      ),
      child: Center(
        child: Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
      ),
    ),
  );
}
