import 'package:darve/components/common/showSnackbar.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:darve/utils/interfaces/Follower.dart';
import 'package:flutter/material.dart';
import '../components/ProfilePage/ProfileCard.dart';

class ProfileInsights extends StatefulWidget {
  final int initialTab;
  final String userId;

  const ProfileInsights({
    super.key,
    required this.userId,
    this.initialTab = 0,
  });

  @override
  State<ProfileInsights> createState() => _ProfileInsightsState();
}

class _ProfileInsightsState extends State<ProfileInsights> {
  List<Follower> following = [];
  List<Follower> followers = [];

  Future<void> refreshFollowing() async {
    var _following = await ServiceProvider.profileRepository.getFollowing(widget.userId);
    var _followers = await ServiceProvider.profileRepository.getFollowers(widget.userId);
    setState(() {
      following = _following;
      followers = _followers;
    });
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    refreshFollowing();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      initialIndex: widget.initialTab,
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'Profile Insights',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black,
              fontSize: 22,
            ),
          ),
          backgroundColor: Colors.white,
          elevation: 0,
          bottom: const TabBar(
            indicatorSize: TabBarIndicatorSize.tab,
            labelColor: Colors.black,
            unselectedLabelColor: Colors.grey,
            tabs: [
              Tab(text: 'Followers'),
              Tab(text: 'Following'),
            ],
          ),
        ),
        body: Container(
          color: Colors.white,
          child: TabBarView(
            children: [
              // Followers Tab
              followers.isEmpty
                  ? const Center(
                      child: Text(
                      "No Followers yet",
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ))
                  : GridView.builder(
                      padding: const EdgeInsets.all(16),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 1,
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                      ),
                      itemCount: followers.length,
                      itemBuilder: (context, index) {
                        return ProfileCard(
                          userName: followers[index].username,
                          subText: 'Followed by Alex and ${index + 3} others',
                          imageUrl: followers[index].imageUrl,
                          btnVal: 'Remove',
                          onBtnClick: () {},
                        );
                      },
                    ),

              following.isEmpty
                  ? const Center(
                      child: Text(
                      "User not following anyone.",
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ))
                  : GridView.builder(
                      padding: const EdgeInsets.all(16),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 1,
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                      ),
                      itemCount: following.length,
                      itemBuilder: (context, index) {
                        return ProfileCard(
                          userName: following[index].username,
                          subText: 'Followed by Sarah and ${index + 2} others',
                          imageUrl: following[index].imageUrl,
                          btnVal: 'Following',
                          onBtnClick: () async {
                            final details = await ServiceProvider.profileRepository
                                .getProfileData(following[index].username);
                            await ServiceProvider.profileRepository
                                .unfollowUser(details.userId!);
                            await refreshFollowing();
                            SnackbarHelper.showFollowSnackbar(
                                context: context,
                                imageUri: details.imageUri!,
                                username: details.userName!,
                                bgColor: Colors.red,
                                isFollowed: false);
                          },
                        );
                      },
                    ),
            ],
          ),
        ),
      ),
    );
  }
}
