import 'package:darve/components/RecordingsPage/ChallengeBox.dart';
import 'package:darve/routes/route_helper.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class RecordingsPage extends StatelessWidget {
  const RecordingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Recordings',
          style: TextStyle(
            fontWeight: FontWeight.w400,
            color: Colors.black, // Black color
            fontSize: 20, // Font size
          ),
        ),
        backgroundColor: Colors.white, // Transparent background for app bar
        elevation: 0, // Remove the shadow
      ),
      body: Container(
        color: Colors.white, // White background color
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  "Our challenges",
                  style: TextStyle(fontWeight: FontWeight.w400, fontSize: 18),
                ),
              ),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    ChallengeBox(
                      index: 1,
                      title: 'SuperChallenge',
                      body:
                          'This is the description of the challenge.s the description of the challenge.',
                      onPressed: () {
                        debugPrint('Challenge Started!');
                      },
                    ),
                    ChallengeBox(
                      index: 2,
                      title: 'Challenge 1',
                      body:
                          'This is the description of the challenge.s the description of the challenge.',
                      onPressed: () {
                        debugPrint('Challenge Started!');
                      },
                    )
                  ],
                ),
              ),
              const SizedBox(height: 16.0),
              Center(
                child: Column(
                  children: [
                    // Image.asset(
                    //   'assets/images/challenges/line.png',
                    //   width: 40,
                    // ),
                    // const Text(
                    //   "SWIPING FOR MORE CHALLENGES",
                    //   style: TextStyle(
                    //     fontWeight: FontWeight.w300,
                    //     fontSize: 10,
                    //     color: Colors.black26,
                    //   ),
                    // ),
                    const Text(
                      "These are Our Recommended Challenges For",
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        color: Styles.textLightColor,
                      ),
                    ),
                    const Text(
                      "Beginning of your Darve Career",
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        color: Styles.textLightColor,
                      ),
                    ),
                    const SizedBox(height: 54.0),
                    Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: ElevatedButton(
                            onPressed: () {},
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(50.0),
                              ),
                              minimumSize: const Size(double.infinity, 50),
                              elevation: 1,
                            ),
                            child: const Text(
                              "Live Streaming",
                              style: TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.w500,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16.0),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: ElevatedButton(
                            onPressed: () {
                              RouteHelper.goToMyChallenges();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Styles.primaryColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(50.0),
                              ),
                              minimumSize: const Size(double.infinity, 50),
                            ),
                            child: const Text(
                              "My challenges",
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 12.0),
                        const Text(
                          "Here You Can Find All Your Existing Challenges",
                          style: TextStyle(
                              fontWeight: FontWeight.w400,
                              color: Styles.textLightColor,
                              fontSize: 12),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
