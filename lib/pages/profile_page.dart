import 'package:darve/components/ProfilePage/ButtonsGroup.dart';
import 'package:darve/components/ProfilePage/CreatePostBtn.dart';
import 'package:darve/components/ProfilePage/SocialGroup.dart';
import 'package:darve/components/ProfilePage/UserAggregate.dart';
import 'package:darve/components/ProfilePage/post_modal.dart';
import 'package:darve/components/common/TopBar.dart';
import 'package:darve/controllers/chat_controller.dart';
import 'package:darve/controllers/profile_controller.dart';
import 'package:darve/helpers/user_helper.dart';
import 'package:darve/routes/route_helper.dart';
import 'package:darve/utils/interfaces/Post.dart';
import 'package:darve/utils/medialinks.dart';
import 'package:darve/utils/serverAssets.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../components/PostCard.dart';
import '../components/common/LinearCard.dart';
import 'package:sliding_up_panel2/sliding_up_panel2.dart';

class ProfilePage extends StatefulWidget {
  final String userId;
  final String username;
  final String imageUrl;
  final String fullName;
  final VoidCallback? goBackToReels;
  const ProfilePage(this.userId, this.username, this.imageUrl, this.fullName,
      {this.goBackToReels, super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  late final ScrollController scrollController;
  final ScrollController _scrollController = ScrollController();
  List<Post> posts = [];

  ChatController chatController = Get.put(ChatController());
  ProfileController? profileController;

  void _showPostModal(BuildContext context) {
    PostModal(
      (pickedFilePath, content) async {
        if (content.isEmpty || pickedFilePath.isEmpty) {
          return;
        }
        Navigator.of(context).pop();
        profileController!.content.value = content;
        await profileController!.makePost(pickedFilePath);
        pickedFilePath = "";
      },
      (content) {
        profileController!.content.value = content;
      },
      '',
    ).showPostModal(context);
  }

  @override
  void initState() {
    super.initState();
    scrollController = ScrollController();
    profileController = Get.put(
        ProfileController(userId: widget.userId, userName: widget.username),
        tag: widget.userId);
    profileController!.profileDataReload();
  }

  void createChatWithUserId() async {
    var discussionId = await chatController.createChatWithUserId(widget.userId);
    debugPrint("discussionId===$discussionId");

    if (discussionId != null) {
      RouteHelper.goToChat(
        chatId: discussionId,
        title: widget.username,
        avatarUrl: widget.imageUrl,
        userId: widget.userId,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (profileController != null && profileController!.isMe.value) {
      profileController!.profileDataReload();
    }
    return SlidingUpPanel(
        maxHeight: MediaQuery.of(context).size.height * 0.80,
        minHeight: MediaQuery.of(context).size.height *
            (profileController!.isMe.value ? 0.50 : 0.55),
        body: Stack(
          children: [
            if (widget.imageUrl.isNotEmpty)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Image.network(
                  ServerAssets().getAssetUrl(widget.imageUrl),
                  height: 450,
                  fit: BoxFit.cover,
                ),
              ),
            Obx(() {
              return TopBar(
                  title: "Profile",
                  isTransparent: true,
                  isCurrentUser: profileController!.isMe.value,
                  callback: widget.goBackToReels);
            })
          ],
        ),
        scrollController: scrollController,
        borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(40.0), topRight: Radius.circular(40.0)),
        panelBuilder: () => SizedBox(
              height: double.infinity,
              child: SingleChildScrollView(
                controller: scrollController,
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Obx(() {
                      return Column(
                        children: [
                          Text(
                            widget.fullName,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 2),
                          if (profileController!
                              .userProfile.value.bio!.isNotEmpty)
                            SizedBox(
                              width: MediaQuery.of(context).size.width * 0.8,
                              child: Text(
                                profileController!.userProfile.value.bio!,
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          const SizedBox(height: 16),
                          SocialGroup(
                              profileController!.userProfile.value.socialLinks!,
                              profileController!.userProfile.value.platforms),
                          const SizedBox(height: 24),
                          UserAggregate(
                              profileController!.userProfile.value.followingNr
                                  .toString(),
                              profileController!.userProfile.value.followersNr
                                  .toString(),
                              posts,
                              widget.userId),
                          if (!profileController!.isMe.value)
                            const SizedBox(height: 24),
                          if (!profileController!.isMe.value)
                            ProfileButtonsGroup(
                              widget.userId,
                              widget.username,
                              widget.imageUrl,
                              profileController!.profileDataReload,
                              createChatWithUserId,
                            ),
                          const SizedBox(height: 24),
                          // ElevatedButton(
                          //   onPressed: handlePostButtonClick,
                          //   child: Text("Post"),
                          // ),
                          SizedBox(
                            height: 500,
                            child: DefaultTabController(
                              length: 2,
                              child: Builder(
                                builder: (context) {
                                  final TabController tabController =
                                      DefaultTabController.of(context);
                                  tabController.addListener(() {
                                    setState(() {});
                                  });

                                  return Column(
                                    children: [
                                      TabBar(
                                        indicatorColor: Colors.black,
                                        indicatorWeight: 1,
                                        indicatorSize: TabBarIndicatorSize.tab,
                                        tabs: [
                                          Tab(
                                            icon: Image.asset(
                                                'assets/images/profile/feed.png',
                                                color: Colors.black,
                                                width: 25,
                                                height: 25,
                                                semanticLabel: 'Tab 1'),
                                          ),
                                          Tab(
                                            icon: Image.asset(
                                                'assets/images/profile/people.png',
                                                color: Colors.black,
                                                width: 25,
                                                height: 25,
                                                semanticLabel: 'Tab 2'),
                                          ),
                                        ],
                                      ),
                                      Expanded(
                                        child: TabBarView(
                                          children: [
                                            SingleChildScrollView(
                                              physics:
                                                  const AlwaysScrollableScrollPhysics(),
                                              child: posts.isEmpty
                                                  ? Padding(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              16.0),
                                                      child: Obx(() {
                                                        return profileController!
                                                                .loadingPosts
                                                                .value
                                                            ? const Center(
                                                                child:
                                                                    CircularProgressIndicator())
                                                            : profileController!
                                                                    .isMe.value
                                                                ? SizedBox(
                                                                    height: 180,
                                                                    width: 120,
                                                                    child: CreatePostBtn(
                                                                        _showPostModal))
                                                                : const Center(
                                                                    child: Text(
                                                                        "No Posts yet!",
                                                                        style: Styles
                                                                            .lightTextStyle),
                                                                  );
                                                      }),
                                                    )
                                                  : Column(
                                                      children: [
                                                        GridView.builder(
                                                          physics:
                                                              const NeverScrollableScrollPhysics(),
                                                          shrinkWrap: true,
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(8),
                                                          gridDelegate:
                                                              const SliverGridDelegateWithFixedCrossAxisCount(
                                                            crossAxisCount: 3,
                                                            crossAxisSpacing: 2,
                                                            mainAxisSpacing: 8,
                                                            childAspectRatio:
                                                                0.6,
                                                          ),
                                                          itemCount: posts
                                                                  .length +
                                                              (profileController!
                                                                      .isMe
                                                                      .value
                                                                  ? 1
                                                                  : 0),
                                                          itemBuilder:
                                                              (context, index) {
                                                            if (profileController!
                                                                    .isMe
                                                                    .value &&
                                                                index == 0) {
                                                              return CreatePostBtn(
                                                                  _showPostModal);
                                                            } else {
                                                              final postIndex =
                                                                  profileController!
                                                                          .isMe
                                                                          .value
                                                                      ? index -
                                                                          1
                                                                      : index;
                                                              return PostCard(
                                                                post: posts[
                                                                    postIndex],
                                                                imageUrl: MediaLinksHelper()
                                                                    .getPostReel(
                                                                        posts[
                                                                            postIndex]),
                                                              );
                                                            }
                                                          },
                                                        ),
                                                      ],
                                                    ),
                                            ),
                                            ListView.builder(
                                              physics:
                                                  const AlwaysScrollableScrollPhysics(),
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 8),
                                              itemCount: profileController!
                                                  .followers.length,
                                              itemBuilder: (context, index) {
                                                return LinearCard(
                                                  userName: profileController!
                                                      .followers[index]
                                                      .username,
                                                  imageUrl: profileController!
                                                      .followers[index]
                                                      .imageUrl,
                                                );
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      );
                    }),
                  ),
                ),
              ),
            ));
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
