import 'package:darve/components/ReelsPage/ActionBtnsGroup.dart';
import 'package:darve/components/ReelsPage/ChallengeTile.dart';
import 'package:darve/components/ReelsPage/CommentTile.dart';
import 'package:darve/components/ReelsPage/ReelsIcon.dart';
import 'package:darve/components/ReelsPage/ToggleComments.dart';
import 'package:darve/components/ReelsPage/User.dart';
import 'package:darve/components/common/ReelChild.dart';
import 'package:darve/mobx/UserStore/user_store.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:darve/utils/init.dart';
import 'package:darve/utils/interfaces/Post.dart';
import 'package:darve/utils/interfaces/ShortenedChallenge.dart';
import 'package:darve/utils/interfaces/ShortenedComment.dart';
import 'package:darve/utils/medialinks.dart';
import 'package:darve/utils/participants.dart';
import 'package:darve/utils/serverAssets.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class ReelsPage extends StatefulWidget {
  const ReelsPage({super.key});

  @override
  State<ReelsPage> createState() => _ReelsPageState();
}

class _ReelsPageState extends State<ReelsPage> {
  final UserStore _userStore = Darve.instance.userStore;
  List<Post> trendingReels = [];

  List<Post> followingReels = [];

  List<Post> currentReels = [];
  bool isTrending = false; //sets default reels to follower reels

  Post? post;
  List posts = [];

  List<ShortenedComment> comments = [];

  List<ShortenedChallenge> challenges = [];

  bool isChallengeSelected = false;

  void onChallengeChange(bool _isChallengeSelected) async {
    setState(() {
      isChallengeSelected = _isChallengeSelected;
    });
  }

  Future<List<ShortenedChallenge>> loadChallenges() async {
    var _challenges = await ServiceProvider.challengeRepository.getGivenTaskRequestsForPost(post!.id);
    List<ShortenedChallenge> parsedChallenges = [];

    for (var i = 0; i < _challenges.length; i++) {
      var challengeUserDetails =
          await ServiceProvider.profileRepository.getProfileData(_challenges[i].fromUserId);

      parsedChallenges.add(ShortenedChallenge(
          id: _challenges[i].id,
          avatar: challengeUserDetails.imageUri,
          username: challengeUserDetails.userName,
          challenge: _challenges[i].requestText,
          bet: cumulateBalance(_challenges[i].participants),
          participants: _challenges[i].participants));
    }

    setState(() {
      challenges = parsedChallenges;
    });
    return parsedChallenges;
  }

  void handlePostChallenge(String value, Function modalSetState) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        bool showCustomField = false;
        TextEditingController customController = TextEditingController();

        return AlertDialog(
          backgroundColor: Colors.white,
          title: const Text(
            'Select Challenge Amount',
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18),
          ),
          content: StatefulBuilder(
            builder: (context, setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (!showCustomField) ...[
                    _buildButton(context, "\$10.0", () {
                      addChallenge(value, modalSetState, 10.0);
                      Navigator.pop(context);
                    }),
                    _buildButton(context, "\$20.0", () {
                      addChallenge(value, modalSetState, 20.0);
                      Navigator.pop(context);
                    }),
                    _buildButton(context, "\$30.0", () {
                      addChallenge(value, modalSetState, 30.0);
                      Navigator.pop(context);
                    }),
                    _buildButton(context, "Custom", () {
                      setState(() {
                        showCustomField = true;
                      });
                    }),
                  ],
                  if (showCustomField) ...[
                    TextField(
                      controller: customController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        hintText: 'Enter custom amount',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                      ),
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: () {
                        double? customAmount =
                            double.tryParse(customController.text);
                        if (customAmount != null && customAmount > 0) {
                          addChallenge(value, modalSetState, customAmount);
                          Navigator.pop(context);
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Styles.primaryColor,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 50, vertical: 6),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                      child: const Text(
                        "Submit",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ],
              );
            },
          ),
        );
      },
    );
  }

  void addChallenge(
      String value, Function modalSetState, double challengeAmount) async {
    var userDetails = await ServiceProvider.profileRepository.getProfileData(post!.username);
    var toUserId = userDetails.userId;

    //checking if content includes username
    bool containsUsername = value.contains("@");
    if (containsUsername) {
      var usernameToBeChallenged = value.split("@")[1].split(" ")[0];
      userDetails =
          await ServiceProvider.profileRepository.getProfileData(usernameToBeChallenged);
      if (userDetails.userId != null) {
        toUserId = userDetails.userId;
      }
    }

    await ServiceProvider.challengeRepository.createTaskRequest(
      toUserId: toUserId!,
      content: value,
      postId: post!.id,
      offerAmount: challengeAmount,
    );

    var updatedChallenges = await ServiceProvider.challengeRepository.getGivenTaskRequestsForPost(post!.id);
    List<ShortenedChallenge> parsedChallenges = [];

    for (var challenge in updatedChallenges) {
      var challengeUserDetails =
          await ServiceProvider.profileRepository.getProfileData(challenge.fromUserId);
      parsedChallenges.add(ShortenedChallenge(
          id: challenge.id,
          avatar: challengeUserDetails.imageUri,
          username: challengeUserDetails.userName,
          challenge: challenge.requestText,
          bet: challengeAmount.toString(),
          participants: challenge.participants));
    }

    modalSetState(() {
      challenges = parsedChallenges;
    });
  }

  void _showCommentModal() {
    TextEditingController commentController = TextEditingController();

    void handleComment(String value, Function modalSetState) async {
      if (value.trim().isNotEmpty) {
        if (isChallengeSelected) {
          handlePostChallenge(value, modalSetState);
        } else {
          if (post == null) return;
          var commentResponse = await ServiceProvider.postsRepository.postInDiscussion(
            post!.belongsToId,
            post!.postTitleUri,
            value,
          );

          if (commentResponse) {
            modalSetState(() {
              comments.insert(
                  0,
                  ShortenedComment(
                      avatar: _userStore.image_uri,
                      username: _userStore.username,
                      comment: value,
                      likes: "0"));
            });
          } else {
            Navigator.pop(context);
          }
        }
        commentController.clear();
      }
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext modalContext, modalSetState) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(30),
                ),
                padding: const EdgeInsets.all(16.0),
                height: MediaQuery.of(context).size.height * 0.6,
                child: Column(
                  children: [
                    ToggleComments(
                      isChallengeSelected: isChallengeSelected,
                      onChange: (value) {
                        modalSetState(() {
                          isChallengeSelected = value;
                        });
                      },
                    ),
                    Expanded(
                      child: isChallengeSelected
                          ? challenges.isEmpty
                              ? Column(
                                  children: [
                                    Divider(
                                      color: Colors.grey[100],
                                    ),
                                    const SizedBox(
                                      height: 8.0,
                                    ),
                                    const Text(
                                      "No Challenges yet!",
                                      style: Styles.lightTextStyle,
                                    ),
                                  ],
                                )
                              : ListView.builder(
                                  itemCount: challenges.length,
                                  itemBuilder: (context, index) {
                                    return ChallengeTile(
                                        challenges[index], loadChallenges);
                                  },
                                )
                          : comments.isEmpty
                              ? Column(
                                  children: [
                                    Divider(
                                      color: Colors.grey[100],
                                    ),
                                    const SizedBox(
                                      height: 8.0,
                                    ),
                                    const Text(
                                      "No Comments yet!",
                                      style: Styles.lightTextStyle,
                                    ),
                                  ],
                                )
                              : ListView.separated(
                                  itemCount: comments.length,
                                  itemBuilder: (context, index) {
                                    return CommentTile(comments[index]);
                                  },
                                  separatorBuilder: (context, index) =>
                                      const Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 16.0),
                                    child: Divider(
                                      color: Colors.grey,
                                      height: 1.0,
                                    ),
                                  ),
                                ),
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          width: 52,
                          height: 52,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Styles.primaryColor,
                              width: 2.0,
                            ),
                          ),
                          child: CircleAvatar(
                            radius: 24,
                            backgroundImage: NetworkImage(ServerAssets()
                                .getAssetUrl(_userStore.image_uri!)),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: TextField(
                            controller: commentController,
                            onSubmitted: (value) {
                              handleComment(value, modalSetState);
                            },
                            decoration: InputDecoration(
                              hintText:
                                  isChallengeSelected ? 'Challenge' : 'Comment',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(25),
                              ),
                              filled: true,
                              fillColor: Colors.white,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 15,
                                vertical: 4,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          decoration: const BoxDecoration(
                            color: Styles.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(Icons.send, color: Colors.white),
                            onPressed: () {
                              handleComment(
                                  commentController.text, modalSetState);
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  @override
  void initState() {
    super.initState();
    ServiceProvider.postsRepository.getFollowingPosts().then((fetchedPosts) {
      setState(() {
        currentReels = fetchedPosts; //sets current reels to followers posts
        followingReels = fetchedPosts;
        posts = fetchedPosts;
        post = fetchedPosts.isNotEmpty ? fetchedPosts[0] : null;
      });
      refreshComments(fetchedPosts.isEmpty ? null : fetchedPosts[0]);
    });
  }

  Future<void> precacheImages(BuildContext context, int currentIndex) async {
    if (currentIndex + 1 < currentReels.length) {
      await precacheImage(
          NetworkImage(
              MediaLinksHelper().getPostReel(currentReels[currentIndex + 1])),
          context);
    }
  }

  void toggleReels(bool showTrending) {
    var _currentReels = showTrending ? trendingReels : followingReels;
    setState(() {
      isTrending = showTrending;
      currentReels = _currentReels;
    });
    refreshComments(_currentReels.isEmpty ? null : _currentReels[0]);
  }

  Future<void> refreshComments(Post? post) async {
    if (post == null) return;
    var val = await ServiceProvider.postsRepository.getPostDiscussions(post.belongsToId, post.id);
    setState(() {
      comments = val;
    });
    await loadChallenges();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const PreferredSize(
        preferredSize: Size.zero,
        child: SizedBox.shrink(),
      ),
      body: currentReels.isEmpty
          ? Center(
              child: Container(
                color: Colors.white,
                child: Stack(
                  children: [
                    const Positioned(
                      top: 60,
                      right: 16.0,
                      child: ActionBtnsGroup(),
                    ),
                    SizedBox.expand(
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              "No Posts in ${currentReels != trendingReels ? "Follow Section" : "Trending Section"}",
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                  color: Styles.textLightColor),
                            ),
                            const SizedBox(height: 4),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 8.0),
                              child: ElevatedButton(
                                onPressed: () {
                                  toggleReels(currentReels == followingReels);
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Styles.primaryColor,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(50.0),
                                  ),
                                  minimumSize: const Size(double.infinity, 40),
                                ),
                                child: Ink(
                                  child: Text(
                                    "Switch to ${currentReels == trendingReels ? "Follow Section" : "Trending Section"}",
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      letterSpacing: -0.4,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            )
          : Stack(
              children: [
                PageView.builder(
                  scrollDirection: Axis.vertical,
                  itemCount: currentReels.length,
                  onPageChanged: (index) {
                    setState(() {
                      post = posts[index];
                    });
                    precacheImages(context, index);
                    refreshComments(currentReels[index]);
                  },
                  itemBuilder: (context, index) {
                    return Stack(
                      children: [
                        // Background image
                        ReelChild(currentReels[index]),
                        // Container(
                        //   decoration: BoxDecoration(
                        //     image: DecorationImage(
                        //       image: NetworkImage(MediaLinksHelper()
                        //           .getPostReel(currentReels[index])),
                        //       fit: BoxFit.cover,
                        //     ),
                        //   ),
                        // ),
                        ReelUser(currentReels[index].username,
                            currentReels[index].createdOn),
                        // Top right notification icon
                        const Positioned(
                          top: 60,
                          right: 16.0,
                          child: ActionBtnsGroup(),
                        ),

                        // Bottom right action buttons
                        Positioned(
                          bottom: 80,
                          right: 10,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              //todo replace with likes
                              ReelsIcon(
                                  'assets/images/home/<USER>', () {}, "0"),
                              const SizedBox(height: 20),
                              ReelsIcon(
                                  'assets/images/home/<USER>',
                                  _showCommentModal,
                                  comments.length.toString()),
                              const SizedBox(height: 20),
                              //Todo replace with shares counts
                              ReelsIcon(
                                  'assets/images/home/<USER>', () {}, "0"),
                              const SizedBox(height: 20),
                              ReelsIcon(
                                  'assets/images/home/<USER>', () {}, "More"),
                            ],
                          ),
                        ),
                      ],
                    );
                  },
                ),

                // Bottom center toggle button
                Positioned(
                  bottom: 40,
                  left: 0,
                  right: 0,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      GestureDetector(
                        onTap: () => toggleReels(true),
                        child: Text(
                          'Trending',
                          style: TextStyle(
                            color: isTrending ? Colors.white : Colors.grey[400],
                            fontSize: 16,
                            fontWeight:
                                isTrending ? FontWeight.bold : FontWeight.w600,
                          ),
                        ),
                      ),
                      const SizedBox(width: 20),
                      GestureDetector(
                        onTap: () => toggleReels(false),
                        child: Text(
                          'Following',
                          style: TextStyle(
                            color: !isTrending ? Colors.white : Colors.white54,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }
}

Widget _buildButton(
    BuildContext context, String title, VoidCallback handleClick) {
  return Container(
    margin: const EdgeInsets.all(4),
    child: TextButton(
      onPressed: handleClick,
      style: TextButton.styleFrom(
        minimumSize: const Size(double.infinity, 40),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.grey[200],
      ),
      child: Row(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              title,
              style: const TextStyle(fontSize: 16, color: Colors.black),
            ),
          ),
        ],
      ),
    ),
  );
}
