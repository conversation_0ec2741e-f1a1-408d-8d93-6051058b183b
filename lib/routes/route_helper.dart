import 'package:get/get.dart';
import 'package:darve/routes/app_routes.dart';

class RouteHelper {
  // Navigation methods
  static void goToLogin() {
    Get.offAllNamed(AppRoutes.login);
  }

  static void goToHome() {
    Get.offAllNamed(AppRoutes.home);
  }

  static void goToChat({
    required String chatId,
    required String title,
    required String avatarUrl,
    required String userId,
  }) {
    Get.toNamed(
      AppRoutes.chat,
      arguments: {
        'chatId': chatId,
        'title': title,
        'avatarUrl': avatarUrl,
        'userId': userId,
      },
    );
  }

  // Check current route
  static bool get isOnLoginPage => Get.currentRoute == AppRoutes.login;
  static bool get isOnHomePage => Get.currentRoute == AppRoutes.home;
  static bool get isOnChatPage => Get.currentRoute == AppRoutes.chat;
}
