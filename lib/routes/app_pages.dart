import 'package:get/get.dart';
import 'package:darve/pages/SignIn.dart';
import 'package:darve/pages/ScreenHandler.dart';
import 'package:darve/pages/ChatPages/chat_screen.dart';
import 'package:darve/controllers/auth_controller.dart';
import 'package:darve/controllers/chat_list_controller.dart';
import 'package:darve/routes/app_routes.dart';
import 'package:darve/routes/auth_middleware.dart';

class AppPages {
  static final List<GetPage> pages = [
    // Login page - no middleware needed
    GetPage(
      name: AppRoutes.login,
      page: () => SignInPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<AuthController>(() => AuthController());
      }),
    ),
    
    // Home page - requires authentication
    GetPage(
      name: AppRoutes.home,
      page: () => const ScreenHandler(),
      middlewares: [AuthMiddleware()],
      binding: BindingsBuilder(() {
        Get.lazyPut<AuthController>(() => AuthController());
        Get.lazyPut<ChatListController>(() => ChatListController());
      }),
    ),
    
    // Chat screen - requires authentication
    GetPage(
      name: AppRoutes.chat,
      page: () {
        final args = Get.arguments as Map<String, String?>;
        return ChatScreen(
          chatId: args['chatId']!,
          title: args['title']!,
          avatarUrl: args['avatarUrl']!,
          userId: args['userId']!,
        );
      },
      middlewares: [AuthMiddleware()],
    ),
  ];
}
