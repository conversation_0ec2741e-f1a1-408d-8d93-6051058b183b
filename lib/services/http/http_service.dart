import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:darve/config.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class HttpService {
  late final Dio _dio;
  String? authToken;

  HttpService() {
    _dio = Dio();
    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.options.baseUrl = AppConfig.instance.apiUrl;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.sendTimeout = const Duration(seconds: 30);

    // Add comprehensive logging interceptor (only in debug mode)
    if (kDebugMode) {
      _dio.interceptors.add(_createCustomLogInterceptor());
    }

    // Request interceptor for adding auth headers
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add common headers
          options.headers.addAll({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          });

          // Add auth token if available
          final token = authToken;
          if (token != null && token.isNotEmpty) {
            options.headers['Cookie'] = 'jwt=$token';
          }

          handler.next(options);
        },
        onResponse: (response, handler) {
          handler.next(response);
        },
        onError: (error, handler) {
          handler.next(error);
        },
      ),
    );
  }

  // GET request
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // GET request with stream response
  Stream<String?> getStream(
    String path, {
    Map<String, dynamic>? queryParameters,
  }) async* {
    try {
      final completePath =
          path.startsWith('http') ? path : _dio.options.baseUrl + path;
      final request = http.Request('GET', Uri.parse(completePath));
      request.headers.addAll({
        'Content-Type': 'text/event-stream',
        'Cookie': 'jwt=$authToken',
        'Accept': 'application/json',
      });

      final response = await request.send();
      if (response.statusCode != 200) {
        throw Exception('Failed to connect to SSE: ${response.reasonPhrase}');
      }

      final stream =
          response.stream.transform(utf8.decoder).asBroadcastStream();

      await for (var line in stream) {
        if (line.startsWith('data:')) {
          final jsonData = line.substring(5).trim();
          try {
            // Attempt to decode the JSON data
            yield jsonData;
          } catch (e) {
            // If decoding fails, log the error and send a message back
            debugPrint('Invalid JSON received in SSE data: $jsonData');
            yield null;
          }
        } else {
          // Optionally, yield an error or handle it in a different way
          yield null;
        }
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // POST request
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // PUT request
  Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.put<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // DELETE request
  Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.delete<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Handle Dio errors and convert to app-specific exceptions
  Exception _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return Exception(
            'Connection timeout. Please check your internet connection.');

      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data?.toString() ?? error.message;

        switch (statusCode) {
          case 401:
            return Exception('Unauthorized. Please log in again.');
          case 403:
            return Exception('Access forbidden.');
          case 404:
            return Exception('Resource not found.');
          case 500:
            return Exception('Server error. Please try again later.');
          default:
            return Exception('Request failed: $message');
        }

      case DioExceptionType.cancel:
        return Exception('Request was cancelled.');

      case DioExceptionType.connectionError:
        return Exception('No internet connection.');

      default:
        return Exception('Network error: ${error.message}');
    }
  }

  // Create custom log interceptor with detailed formatting
  InterceptorsWrapper _createCustomLogInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) {
        final uri = options.uri.toString();
        final method = options.method.toUpperCase();

        debugPrint('\n' + '=' * 80);
        debugPrint('🚀 HTTP REQUEST');
        debugPrint('=' * 80);
        debugPrint('📍 URL: $method $uri');
        debugPrint('⏰ Time: ${DateTime.now().toIso8601String()}');

        // Log headers
        if (options.headers.isNotEmpty) {
          debugPrint('📋 Headers:');
          options.headers.forEach((key, value) {
            // Mask sensitive headers
            if (key.toLowerCase().contains('cookie') ||
                key.toLowerCase().contains('authorization') ||
                key.toLowerCase().contains('token')) {
              debugPrint('   $key: ***MASKED***');
            } else {
              debugPrint('   $key: $value');
            }
          });
        }

        // Log query parameters
        if (options.queryParameters.isNotEmpty) {
          debugPrint('🔍 Query Parameters:');
          options.queryParameters.forEach((key, value) {
            debugPrint('   $key: $value');
          });
        }

        // Log request body
        if (options.data != null) {
          debugPrint('📤 Request Body:');
          try {
            final prettyData = _formatJson(options.data);
            debugPrint(prettyData);
          } catch (e) {
            debugPrint('   ${options.data}');
          }
        }

        debugPrint('=' * 80);

        handler.next(options);
      },
      onResponse: (response, handler) {
        final uri = response.requestOptions.uri.toString();
        final method = response.requestOptions.method.toUpperCase();
        final statusCode = response.statusCode;
        final statusMessage = response.statusMessage ?? '';

        debugPrint('\n' + '=' * 80);
        debugPrint('✅ HTTP RESPONSE');
        debugPrint('=' * 80);
        debugPrint('📍 URL: $method $uri');
        debugPrint('📊 Status: $statusCode $statusMessage');
        debugPrint('⏰ Time: ${DateTime.now().toIso8601String()}');

        // Log response headers
        if (response.headers.map.isNotEmpty) {
          debugPrint('📋 Response Headers:');
          response.headers.map.forEach((key, values) {
            debugPrint('   $key: ${values.join(', ')}');
          });
        }

        // Log response body
        if (response.data != null) {
          debugPrint('📥 Response Body:');
          try {
            final prettyData = _formatJson(response.data);
            // Limit response body size for readability
            if (prettyData.length > 2000) {
              debugPrint('${prettyData.substring(0, 2000)}...\n[TRUNCATED - Response too large]');
            } else {
              debugPrint(prettyData);
            }
          } catch (e) {
            final dataStr = response.data.toString();
            if (dataStr.length > 2000) {
              debugPrint('${dataStr.substring(0, 2000)}...\n[TRUNCATED - Response too large]');
            } else {
              debugPrint(dataStr);
            }
          }
        }

        debugPrint('=' * 80);

        handler.next(response);
      },
      onError: (error, handler) {
        final uri = error.requestOptions.uri.toString();
        final method = error.requestOptions.method.toUpperCase();

        debugPrint('\n' + '=' * 80);
        debugPrint('❌ HTTP ERROR');
        debugPrint('=' * 80);
        debugPrint('📍 URL: $method $uri');
        debugPrint('⏰ Time: ${DateTime.now().toIso8601String()}');
        debugPrint('🚨 Error Type: ${error.type}');
        debugPrint('💬 Error Message: ${error.message}');

        if (error.response != null) {
          final response = error.response!;
          debugPrint('📊 Status Code: ${response.statusCode}');
          debugPrint('📊 Status Message: ${response.statusMessage}');

          if (response.data != null) {
            debugPrint('📥 Error Response:');
            try {
              final prettyData = _formatJson(response.data);
              debugPrint(prettyData);
            } catch (e) {
              debugPrint('   ${response.data}');
            }
          }
        }

        debugPrint('=' * 80);

        handler.next(error);
      },
    );
  }

  // Helper method to format JSON for better readability
  String _formatJson(dynamic data) {
    try {
      if (data is String) {
        // Try to parse as JSON first
        try {
          final parsed = jsonDecode(data);
          return const JsonEncoder.withIndent('  ').convert(parsed);
        } catch (e) {
          // If not JSON, return as is
          return data;
        }
      } else if (data is Map || data is List) {
        return const JsonEncoder.withIndent('  ').convert(data);
      } else {
        return data.toString();
      }
    } catch (e) {
      return data.toString();
    }
  }

  // Get the underlying Dio instance for advanced usage
  Dio get dio => _dio;
}
