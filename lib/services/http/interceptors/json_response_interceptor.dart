import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// Interceptor that handles JSON responses with incorrect Content-Type headers
/// 
/// Many APIs return JSON data but set Content-Type to 'text/html; charset=utf-8'
/// This interceptor automatically detects and parses JSON responses regardless
/// of the Content-Type header.
class JsonResponseInterceptor extends Interceptor {
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // Check if response data is a string and looks like JSON
    if (response.data is String) {
      final responseString = response.data as String;
      final trimmed = responseString.trim();
      
      // Try to parse as JSON if it looks like JSO<PERSON>
      if (trimmed.startsWith('{') || trimmed.startsWith('[')) {
        try {
          final jsonData = jsonDecode(responseString);
          
          if (kDebugMode) {
            final contentType = response.headers.value('content-type') ?? 'unknown';
            debugPrint('🔄 Fixed JSON parsing for Content-Type: $contentType');
          }
          
          // Replace the response data with parsed JSON
          response.data = jsonData;
        } catch (e) {
          if (kDebugMode) {
            debugPrint('⚠️ Could not parse response as JSON: $e');
          }
          // Keep original string data if JSON parsing fails
        }
      }
    }
    
    super.onResponse(response, handler);
  }
}
