import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// Custom HTTP logging interceptor with detailed formatting and security features
/// 
/// Features:
/// - Beautiful formatted logging with emojis and separators
/// - Sensitive data masking (cookies, tokens, etc.)
/// - JSON pretty-printing
/// - Request/response timestamps
/// - Error details with context
/// - Size limiting for large responses
/// - Debug-mode only operation
class CustomLogInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (!kDebugMode) {
      super.onRequest(options, handler);
      return;
    }

    final uri = options.uri.toString();
    final method = options.method.toUpperCase();
    
    debugPrint('\n' + '=' * 80);
    debugPrint('🚀 HTTP REQUEST');
    debugPrint('=' * 80);
    debugPrint('📍 URL: $method $uri');
    debugPrint('⏰ Time: ${DateTime.now().toIso8601String()}');
    
    // Log headers
    if (options.headers.isNotEmpty) {
      debugPrint('📋 Headers:');
      options.headers.forEach((key, value) {
        // Mask sensitive headers
        if (_isSensitiveHeader(key)) {
          debugPrint('   $key: ***MASKED***');
        } else {
          debugPrint('   $key: $value');
        }
      });
    }
    
    // Log query parameters
    if (options.queryParameters.isNotEmpty) {
      debugPrint('🔍 Query Parameters:');
      options.queryParameters.forEach((key, value) {
        debugPrint('   $key: $value');
      });
    }
    
    // Log request body
    if (options.data != null) {
      debugPrint('📤 Request Body:');
      try {
        final prettyData = _formatJson(options.data);
        debugPrint(prettyData);
      } catch (e) {
        debugPrint('   ${options.data}');
      }
    }
    
    debugPrint('=' * 80);
    
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (!kDebugMode) {
      super.onResponse(response, handler);
      return;
    }

    final uri = response.requestOptions.uri.toString();
    final method = response.requestOptions.method.toUpperCase();
    final statusCode = response.statusCode;
    final statusMessage = response.statusMessage ?? '';
    
    debugPrint('\n' + '=' * 80);
    debugPrint('✅ HTTP RESPONSE');
    debugPrint('=' * 80);
    debugPrint('📍 URL: $method $uri');
    debugPrint('📊 Status: $statusCode $statusMessage');
    debugPrint('⏰ Time: ${DateTime.now().toIso8601String()}');
    
    // Log response headers
    if (response.headers.map.isNotEmpty) {
      debugPrint('📋 Response Headers:');
      response.headers.map.forEach((key, values) {
        debugPrint('   $key: ${values.join(', ')}');
      });
    }
    
    // Log response body
    if (response.data != null) {
      debugPrint('📥 Response Body:');
      try {
        final prettyData = _formatJson(response.data);
        // Limit response body size for readability
        if (prettyData.length > 2000) {
          debugPrint('${prettyData.substring(0, 2000)}...\n[TRUNCATED - Response too large]');
        } else {
          debugPrint(prettyData);
        }
      } catch (e) {
        final dataStr = response.data.toString();
        if (dataStr.length > 2000) {
          debugPrint('${dataStr.substring(0, 2000)}...\n[TRUNCATED - Response too large]');
        } else {
          debugPrint(dataStr);
        }
      }
    }
    
    debugPrint('=' * 80);
    
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (!kDebugMode) {
      super.onError(err, handler);
      return;
    }

    final uri = err.requestOptions.uri.toString();
    final method = err.requestOptions.method.toUpperCase();
    
    debugPrint('\n' + '=' * 80);
    debugPrint('❌ HTTP ERROR');
    debugPrint('=' * 80);
    debugPrint('📍 URL: $method $uri');
    debugPrint('⏰ Time: ${DateTime.now().toIso8601String()}');
    debugPrint('🚨 Error Type: ${err.type}');
    debugPrint('💬 Error Message: ${err.message}');
    
    if (err.response != null) {
      final response = err.response!;
      debugPrint('📊 Status Code: ${response.statusCode}');
      debugPrint('📊 Status Message: ${response.statusMessage}');
      
      if (response.data != null) {
        debugPrint('📥 Error Response:');
        try {
          final prettyData = _formatJson(response.data);
          debugPrint(prettyData);
        } catch (e) {
          debugPrint('   ${response.data}');
        }
      }
    }
    
    debugPrint('=' * 80);
    
    super.onError(err, handler);
  }

  /// Check if a header contains sensitive information
  bool _isSensitiveHeader(String key) {
    final lowerKey = key.toLowerCase();
    return lowerKey.contains('cookie') || 
           lowerKey.contains('authorization') ||
           lowerKey.contains('token') ||
           lowerKey.contains('secret') ||
           lowerKey.contains('password');
  }

  /// Helper method to format JSON for better readability
  String _formatJson(dynamic data) {
    try {
      if (data is String) {
        // Try to parse as JSON first
        try {
          final parsed = jsonDecode(data);
          return const JsonEncoder.withIndent('  ').convert(parsed);
        } catch (e) {
          // If not JSON, return as is
          return data;
        }
      } else if (data is Map || data is List) {
        return const JsonEncoder.withIndent('  ').convert(data);
      } else {
        return data.toString();
      }
    } catch (e) {
      return data.toString();
    }
  }
}
