import 'package:flutter_dotenv/flutter_dotenv.dart';

class AppConfig {
  final String apiUrl;
  final String googleAndroidClientId;
  final String googleIosClientId;
  final String stripePublishableKey;

  static AppConfig? _instance;

  AppConfig._(
      {required this.apiUrl,
      required this.googleAndroidClientId,
      required this.googleIosClientId,
      required this.stripePublishableKey});

  static void fromEnv() {
    if (_instance != null) return;
    final apiUrl = dotenv.env['API_URL'];
    final googleAndroidClientId = dotenv.env['GOOGLE_ANDROID_CLIENT_ID'];
    final googleIosClientId = dotenv.env['GOOGLE_IOS_CLIENT_ID'];
    final stripePublishableKey = dotenv.env['STRIPE_PUBLISHABLE_KEY'];

    if (apiUrl == null ||
        googleIosClientId == null ||
        googleAndroidClientId == null || stripePublishableKey == null) {
      throw Exception('Missing required environment variables');
    }

    _instance = AppConfig._(
        apiUrl: apiUrl,
        googleAndroidClientId: googleAndroidClientId,
        googleIosClientId: googleIosClientId,
        stripePublishableKey: stripePublishableKey
      );
  }

  static AppConfig get instance {
    if (_instance == null) {
      throw Exception(
        'AppConfig is not initialized. Call AppConfig.fromEnv() first.',
      );
    }
    return _instance!;
  }
}
