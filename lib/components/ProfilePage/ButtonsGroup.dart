import 'package:darve/components/common/showSnackbar.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class ProfileButtonsGroup extends StatefulWidget {
  final String userId;
  final String username;
  final String imageUrl;
  final Function profileDataReload;
  final Function() createChatWithUserId;
  const ProfileButtonsGroup(this.userId, this.username, this.imageUrl,
      this.profileDataReload, this.createChatWithUserId,
      {super.key});

  @override
  State<ProfileButtonsGroup> createState() => _UserAggregateState();
}

class _UserAggregateState extends State<ProfileButtonsGroup> {
  bool isUserFollowing = false;

  @override
  void initState() {
    // TODO: implement initState
    refreshFollowing();
    super.initState();
  }

  void refreshFollowing() {
    ServiceProvider.profileRepository.isFollowing(widget.userId).then((val) {
      setState(() {
        isUserFollowing = val;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        OutlinedButton(
          onPressed: () async {
            if (isUserFollowing) {
              await ServiceProvider.profileRepository.unfollowUser(widget.userId);
              await widget.profileDataReload();
              SnackbarHelper.showFollowSnackbar(
                  context: context,
                  imageUri: widget.imageUrl,
                  username: widget.username,
                  bgColor: Colors.red,
                  isFollowed: false);
            } else {
              await ServiceProvider.profileRepository.followUser(widget.userId);
              await widget.profileDataReload();
              SnackbarHelper.showFollowSnackbar(
                  context: context,
                  imageUri: widget.imageUrl,
                  username: widget.username,
                  bgColor: Colors.green,
                  isFollowed: true);
            }
            refreshFollowing();
          },
          style: OutlinedButton.styleFrom(
            side: const BorderSide(color: Colors.black),
            padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
          ),
          child: Text(
            isUserFollowing ? 'Unfollow' : 'Follow',
            style: const TextStyle(
              color: Colors.black,
              fontSize: 16,
            ),
          ),
        ),
        const SizedBox(width: 16),
        ElevatedButton(
          onPressed: widget.createChatWithUserId,
          style: ElevatedButton.styleFrom(
            backgroundColor: Styles.primaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 6),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
          ),
          child: const Text(
            'Message',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ],
    );
  }
}
