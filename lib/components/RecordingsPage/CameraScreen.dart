import 'dart:convert';
import 'dart:io';
import 'package:camera/camera.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_player/video_player.dart';
import 'package:vector_math/vector_math_64.dart' as vector;

class CameraScreen extends StatefulWidget {
  final List<CameraDescription> cameras;
  final String taskId;
  final void Function() cb;

  const CameraScreen({
    required this.cameras,
    required this.taskId,
    required this.cb,
    super.key,
  });

  @override
  State<CameraScreen> createState() => _CameraAppState();
}

class _CameraAppState extends State<CameraScreen> {
  late CameraController controller;
  bool isRecording = false;
  String? videoPath;
  VideoPlayerController? videoController;
  bool isVideoPreview = false;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  void _initializeCamera() {
    controller = CameraController(widget.cameras[0], ResolutionPreset.max);
    controller.initialize().then((_) {
      if (!mounted) return;
      setState(() {});
    }).catchError((e) {
      debugPrint("Error initializing camera: $e");
    });
  }

  @override
  void dispose() {
    controller.dispose();
    videoController?.dispose();
    super.dispose();
  }

  Future<void> startRecording() async {
    if (!controller.value.isInitialized) return;
    try {
      await controller.startVideoRecording();
      setState(() {
        isRecording = true;
        isVideoPreview = false;
      });
    } catch (e) {
      debugPrint("Error starting video recording: $e");
    }
  }

  Future<void> stopRecording() async {
    if (!isRecording) return;

    try {
      XFile videoFile = await controller.stopVideoRecording();

      // Get a permanent directory
      final directory = await getApplicationDocumentsDirectory();
      final newFilePath =
          '${directory.path}/${DateTime.now().millisecondsSinceEpoch}.mp4';

      // Move the temp file to a new location
      final File permanentFile = await File(videoFile.path).copy(newFilePath);

      setState(() {
        isRecording = false;
        isVideoPreview = true;
        videoPath = permanentFile.path;
      });

      videoController = VideoPlayerController.file(File(videoPath!))
        ..initialize().then((_) {
          setState(() {});
          videoController!.play();
        });
    } catch (e) {
      debugPrint("Error stopping video recording: $e");
    }
  }

  void replayVideo() {
    if (videoController != null && videoController!.value.isInitialized) {
      videoController!.seekTo(Duration.zero);
      videoController!.play();
    }
  }

  void recordAgain() {
    videoController?.dispose();
    videoController = null;
    setState(() {
      isVideoPreview = false;
      _initializeCamera();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Recording Challenge',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w800,
            fontSize: 18,
          ),
        ),
      ),
      body: Container(
        color: Colors.white,
        child: Column(
          children: [
            Expanded(
              child: isVideoPreview && videoPath != null
                  ? Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(16.0),
                        child: Transform(
                          alignment: Alignment.center,
                          transform: Matrix4.rotationZ(vector.radians(270)),
                          child: AspectRatio(
                            aspectRatio: controller.value.aspectRatio,
                            child: VideoPlayer(videoController!),
                          ),
                        ),
                      ),
                    )
                  : controller.value.isInitialized
                      ? Container(
                          padding: const EdgeInsets.all(16),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(16.0),
                            child: AspectRatio(
                              aspectRatio: controller.value.aspectRatio,
                              child: CameraPreview(controller),
                            ),
                          ),
                        )
                      : const Center(child: CircularProgressIndicator()),
            ),
            const SizedBox(height: 16),
            if (isVideoPreview)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildRoundedButton(
                      icon: Icons.replay,
                      label: "Replay",
                      onPressed: replayVideo),
                  _buildRoundedButton(
                      icon: Icons.videocam,
                      label: "Record Again",
                      onPressed: recordAgain),
                  _buildRoundedButton(
                    icon: Icons.delete,
                    label: "Delete",
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                  _buildRoundedButton(
                    icon: Icons.upload,
                    label: "Submit",
                    onPressed: () async {
                      var response = await ServiceProvider.postsRepository.createPost(
                          "task delivery ${widget.taskId}", () async {
                        debugPrint("executed finally");
                        return true;
                      }, videoPath!);

                      String postId = json.decode(response)['id'];
                      await ServiceProvider.challengeRepository.deliverTaskRequest(
                          widget.taskId, videoPath!, postId);
                      Navigator.pop(context);
                      widget.cb();
                    },
                  ),
                ],
              )
            else
              _buildRoundedButton(
                icon: isRecording ? Icons.stop : Icons.videocam,
                label: isRecording ? "Recording" : "Record",
                onPressed: isRecording ? stopRecording : startRecording,
              ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}

Widget _buildRoundedButton({
  required IconData icon,
  required String label,
  required VoidCallback onPressed,
}) {
  return Column(
    children: [
      InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(50),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
          decoration: BoxDecoration(
            color: label == "Delete" ? Colors.red : Styles.primaryColor,
            borderRadius: BorderRadius.circular(50),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, color: Colors.white, size: 24),
            ],
          ),
        ),
      ),
      const SizedBox(height: 5),
      Text(
        label,
        style: TextStyle(
          fontSize: 12,
          color: label == "Delete" ? Colors.red : Styles.primaryColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    ],
  );
}
