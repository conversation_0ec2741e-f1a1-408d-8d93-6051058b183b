import 'dart:convert';

import 'package:camera/camera.dart';
import 'package:darve/components/RecordingsPage/RecordingIcon.dart';
import 'package:darve/routes/route_helper.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:darve/utils/interfaces/Challenge.dart';
import 'package:darve/utils/interfaces/ProfileData.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../utils/http-requests/challenges/deliverChallenge.dart';

class MyChallengeCard extends StatefulWidget {
  final String username;
  final String avatarUrl;
  final Challenge challenge;
  final void Function() cb;
  final int amount;

  const MyChallengeCard({
    required this.username,
    required this.avatarUrl,
    required this.challenge,
    required this.cb,
    super.key,
    this.amount = 0,
  });

  @override
  State<MyChallengeCard> createState() => _MyChallengeCardState();
}

class _MyChallengeCardState extends State<MyChallengeCard> {
  void cb() {
    RouteHelper.goBack();
    RouteHelper.goToMyChallenges();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 150,
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Stack(
        children: [
          SizedBox(
            height: 114,
            child: Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
                side: widget.challenge.status == ChallengeType.rejected.value
                    ? const BorderSide(color: Colors.red, width: 2)
                    : widget.challenge.status == ChallengeType.delivered.value
                        ? const BorderSide(color: Styles.primaryColor, width: 2)
                        : BorderSide.none,
              ),
              color: Colors.white,
              child: Padding(
                padding: const EdgeInsets.all(15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        GestureDetector(
                          onTap: () async {
                            ProfileData details = await ServiceProvider.profileRepository
                                .getProfileData(widget.username);
                            RouteHelper.goToProfile(
                              userId: details.userId!,
                              username: details.userName!,
                              imageUrl: details.imageUri!,
                              fullName: details.fullName!,
                            );
                          },
                          child: ClipOval(
                            child: Image.network(
                              widget.avatarUrl,
                              width: 40,
                              height: 40,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        const SizedBox(width: 15),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    widget.username,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const Spacer(),
                                  Text(
                                    '${widget.amount}',
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  const Icon(
                                    Icons.monetization_on,
                                    color: Colors.black,
                                    size: 16,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                widget.challenge.requestText,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black54,
                                ),
                                maxLines: 3,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                  ],
                ),
              ),
            ),
          ),
          if (widget.challenge.status == ChallengeType.requested.value)
            Positioned(
              bottom: 0,
              right: 0,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildActionButton("Accept", Icons.check, Styles.primaryColor,
                      () async {
                    await ServiceProvider.challengeRepository.acceptChallenge(widget.challenge.id, true);
                    widget.cb();
                    Future.delayed(const Duration(milliseconds: 100), () {
                      if (mounted) setState(() {});
                    });
                  }),
                  const SizedBox(width: 10),
                  _buildActionButton("Reject", Icons.close, Colors.red,
                      () async {
                    await ServiceProvider.challengeRepository.acceptChallenge(widget.challenge.id, false);
                    widget.cb();
                    Future.delayed(const Duration(milliseconds: 100), () {
                      if (mounted) setState(() {});
                    });
                  }),
                ],
              ),
            ),
          if (widget.challenge.status == ChallengeType.accepted.value)
            Positioned(
              bottom: 0,
              right: 16,
              child: _buildActionButton(kDebugMode ? "Test Deliver" : "Record",
                  null, Styles.primaryColor, () async {
                List<CameraDescription> cameras = await availableCameras();
                if (cameras.isEmpty) {
                  if (kDebugMode) {
                    // deliver task
                    var response = await ServiceProvider.postsRepository.createPost(
                        "task delivery ${widget.challenge.id}", () async {
                      debugPrint("task delivery post created");
                      return true;
                    });

                    String postId = json.decode(response)['id'];
                    await deliverTaskRequest(widget.challenge.id, "", postId);
                    Navigator.pop(context);
                    return;
                  }
                  ErrorsHandle().displayErrorToast("No camera found");
                  return;
                }
                RouteHelper.goToCamera(
                  cameras: cameras,
                  taskId: widget.challenge.id,
                  callback: cb,
                );
              }, isRecord: true),
            ),
          if (widget.challenge.status == ChallengeType.rejected.value)
            Align(
              alignment: Alignment.bottomRight,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: .2),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10),
                    bottomRight: Radius.circular(10),
                  ),
                ),
                child: const Text(
                  "Rejected",
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ),
            ),
          if (widget.challenge.status == ChallengeType.delivered.value)
            Align(
              alignment: Alignment.bottomRight,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
                decoration: BoxDecoration(
                  color: Styles.primaryColor.withValues(alpha: .2),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10),
                    bottomRight: Radius.circular(10),
                  ),
                ),
                child: const Text(
                  "Delivered",
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Styles.primaryColor,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
      String text, IconData? icon, Color color, VoidCallback onPressed,
      {bool isRecord = false}) {
    return Material(
      elevation: 3,
      borderRadius: BorderRadius.circular(50),
      child: InkWell(
        borderRadius: BorderRadius.circular(50),
        onTap: onPressed,
        child: Container(
          height: 40,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(50),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) Icon(icon, color: Colors.white, size: 16),
              if (isRecord) const RecordingIcon(topSize: 16),
              const SizedBox(width: 6),
              Text(
                text,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
