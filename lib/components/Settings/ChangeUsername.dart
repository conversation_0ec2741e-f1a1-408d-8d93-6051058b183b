import 'dart:convert';

import 'package:darve/providers/service_provider.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class ChangeUsername extends StatelessWidget {
  final TextEditingController _usernameController = TextEditingController();

  ChangeUsername({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          const Text(
            "Username",
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _usernameController,
            decoration: InputDecoration(
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: const BorderSide(
                  color: Styles.primaryColor, // Your desired color
                  width: 1.0,
                ),
              ),
              hintText: "Enter new username",
              fillColor: const Color(0xFFEEEEEE),
              filled: true,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: BorderSide.none,
              ),
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 15.0, horizontal: 20.0),
            ),
            style: const TextStyle(
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 42),
          SizedBox(
            width: MediaQuery.of(context).size.width * 0.4,
            child: ElevatedButton(
              onPressed: () async {
                String newUsername = _usernameController.text;
                if (newUsername.isNotEmpty) {
                  Navigator.pop(context, newUsername);
                  await ServiceProvider.profileRepository.editProfile(
                      json.encode({
                        "username": newUsername,
                      }),
                      "");
                }
              },
              child: const Text("Save"),
            ),
          ),
          const SizedBox(height: 180),
        ],
      ),
    );
  }
}
