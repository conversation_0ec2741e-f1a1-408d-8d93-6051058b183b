import 'dart:convert';

import 'package:darve/components/common/ProfilePictureUploader.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:flutter/material.dart';

class ChangeProfilePic extends StatefulWidget {
  const ChangeProfilePic({super.key});

  @override
  State<ChangeProfilePic> createState() => _ChangeProfilePicState();
}

class _ChangeProfilePicState extends State<ChangeProfilePic> {
  String filepath = "";

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const Text(
          "Profile Picture",
          style: TextStyle(fontWeight: FontWeight.w700, fontSize: 16),
        ),
        const SizedBox(height: 14.0),
        ProfilePictureUploader(
          voidCallback: (val) {
            setState(() {
              filepath = val;
            });
          },
          isLarge: true,
        ),
        const SizedBox(height: 40.0),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.4,
          child: ElevatedButton(
            onPressed: () async {
              await ProfileRepository().editProfile(json.encode({}), filepath);
              Navigator.pop(context);
            },
            child: const Text("Save"),
          ),
        ),
        const SizedBox(
          height: 180,
        ),
      ],
    );
  }
}
