import 'package:darve/routes/route_helper.dart';
import 'package:darve/utils/http-requests/profile/profile_repository.dart';
import 'package:darve/utils/serverAssets.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class ChatHeader extends StatelessWidget {
  final String title;
  final String avatarUrl;
  final String chatId;
  final String userId;

  const ChatHeader({
    super.key,
    required this.title,
    required this.avatarUrl,
    required this.chatId,
    required this.userId,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
            bottom: BorderSide(color: Styles.textLightColor, width: 1.0)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: const Icon(Icons.arrow_back_ios_new),
                ),
                const SizedBox(width: 16),
                Stack(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Styles.primaryColor,
                          width: 2,
                        ),
                      ),
                      child: GestureDetector(
                        onTap: () async {
                          final details =
                              await ProfileRepository().getProfileData(userId);
                          RouteHelper.goToProfile(
                            userId: userId,
                            username: details.userName!,
                            imageUrl: details.imageUri!,
                            fullName: details.fullName!,
                          );
                        },
                        child: ClipRRect(
                          borderRadius:
                              BorderRadius.circular(20), // Half of width/height
                          child: Image.network(
                            ServerAssets().getAssetUrl(avatarUrl),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 0.0,
                      right: 0.0,
                      child: Container(
                        height: 12.0,
                        width: 12.0,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(100),
                          color: Colors.greenAccent,
                        ),
                        child: const Text(""),
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 12.0),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    //TODO: replace this with username
                    Text(
                      title,
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 18),
                    ),
                    //TODO: replace with status of user activity
                    const Text(
                      "Active Now",
                      style: TextStyle(
                          color: Styles.textLightColor,
                          fontWeight: FontWeight.w300,
                          fontSize: 14),
                    )
                  ],
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
