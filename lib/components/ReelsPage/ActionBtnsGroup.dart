import 'package:darve/routes/route_helper.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class ActionBtnsGroup extends StatefulWidget {
  const ActionBtnsGroup({super.key});

  @override
  State<ActionBtnsGroup> createState() => _ActionBtnsGroupState();
}

class _ActionBtnsGroupState extends State<ActionBtnsGroup> {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            color: Colors.white,
          ),
          child: IconButton(
            icon: const Icon(
              Icons.search_outlined,
              color: Styles.textLightColor,
            ),
            onPressed: () {
              RouteHelper.goToSearch();
            },
          ),
        ),
        const SizedBox(width: 8.0),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            color: Colors.white,
          ),
          child: <PERSON><PERSON><PERSON><PERSON><PERSON>(
            icon: const Icon(
              Icons.notifications_outlined,
              color: Styles.textLightColor,
            ),
            onPressed: () {
              RouteHelper.goToNotifications();
            },
          ),
        ),
      ],
    );
  }
}
