import 'package:darve/components/ReelsPage/ChallengeAmountBtn.dart';
import 'package:darve/pages/profile_page.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:darve/utils/interfaces/ProfileData.dart';
import 'package:darve/utils/interfaces/ShortenedChallenge.dart';
import 'package:darve/utils/serverAssets.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class ChallengeTile extends StatelessWidget {
  final ShortenedChallenge challenge;
  const ChallengeTile(this.challenge, {super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListTile(
          leading: CircleAvatar(
            backgroundImage: NetworkImage(
              ServerAssets().getAssetUrl(challenge.avatar!),
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    challenge.username!,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  Row(
                    children: [
                      Text(
                        challenge.bet!,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const Icon(
                        Icons.monetization_on_outlined,
                        size: 14,
                      ),
                    ],
                  ),
                ],
              ),
              _buildChallengeText(challenge.challenge!),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    ChallengeAmountBtn("\$10", () {}),
                    ChallengeAmountBtn("\$20", () {}),
                    ChallengeAmountBtn("\$30", () {}),
                    ChallengeAmountBtn("Custom", () {}),
                  ],
                ),
              ),
            ],
          ),
        ),
        const Divider(),
      ],
    );
  }

  Widget _buildChallengeText(String text) {
    final RegExp regExp = RegExp(r'@\w+');
    final List<InlineSpan> spans = [];
    int start = 0;

    for (final match in regExp.allMatches(text)) {
      if (match.start > start) {
        spans.add(TextSpan(
          text: text.substring(start, match.start),
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Styles.textLightColor,
          ),
        ));
      }

      String username = text.substring(match.start, match.end);

      spans.add(
        WidgetSpan(
          alignment: PlaceholderAlignment.baseline,
          baseline: TextBaseline.alphabetic,
          child: FutureBuilder(
            future: ProfileRepository().getProfileData(username),
            builder: (context, AsyncSnapshot snapshot) {
              if (!snapshot.hasData) {
                return Text(
                  username,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Styles.primaryColor,
                  ),
                );
              }

              ProfileData details = snapshot.data as ProfileData;
              return details.userId != null
                  ? GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => Scaffold(
                              body: ProfilePage(
                                details.userId!,
                                details.userName!,
                                details.imageUri!,
                                details.fullName!,
                              ),
                            ),
                          ),
                        );
                      },
                      child: Text(
                        username,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Styles.primaryColor,
                        ),
                      ),
                    )
                  : Text(
                      username,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Styles.textLightColor,
                      ),
                    );
            },
          ),
        ),
      );

      start = match.end;
    }

    if (start < text.length) {
      spans.add(TextSpan(
        text: text.substring(start),
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Styles.textLightColor,
        ),
      ));
    }

    return RichText(text: TextSpan(children: spans));
  }
}
