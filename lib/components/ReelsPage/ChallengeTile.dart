import 'package:darve/components/ReelsPage/ChallengeAmountBtn.dart';
import 'package:darve/pages/profile_page.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:darve/utils/interfaces/Challenge.dart';
import 'package:darve/utils/interfaces/ProfileData.dart';
import 'package:darve/utils/serverAssets.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:darve/utils/interfaces/ShortenedChallenge.dart';

class ChallengeTile extends StatefulWidget {
  final ShortenedChallenge challenge;
  final Future<List<ShortenedChallenge>> Function() cb;

  const ChallengeTile(this.challenge, this.cb, {super.key});

  @override
  State<ChallengeTile> createState() => _ChallengeTileState();
}

class _ChallengeTileState extends State<ChallengeTile> {
  List<Participant> participants = [];

  @override
  void initState() {
    super.initState();
    participants = widget.challenge.participants ?? [];
  }

  void showCustomInput() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        TextEditingController customController = TextEditingController();

        return AlertDialog(
          backgroundColor: Colors.white,
          title: const Text(
            'Select Challenge Amount',
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18),
          ),
          content: StatefulBuilder(
            builder: (context, setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ...[
                    TextField(
                      controller: customController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        hintText: 'Enter custom amount',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                      ),
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: () {
                        double? customAmount =
                            double.tryParse(customController.text);
                        if (customAmount != null && customAmount > 0) {
                          _addParticipant(customAmount.toInt());
                          Navigator.pop(context);
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Styles.primaryColor,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 50, vertical: 6),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                      child: const Text(
                        "Submit",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ],
              );
            },
          ),
        );
      },
    );
  }

  Future<void> _addParticipant(int amount) async {
    try {
      await ServiceProvider.challengeRepository.addParticipant(widget.challenge.id!, amount.toDouble());
      List<ShortenedChallenge> challenges = await widget.cb();

      // Finding challenge with the same ID
      for (var i = 0; i < challenges.length; i++) {
        if (challenges[i].id == widget.challenge.id) {
          setState(() {
            participants = challenges[i].participants ?? [];
          });
        }
      }
    } catch (error) {
      debugPrint("Failed to add participant: $error");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            leading: CircleAvatar(
              backgroundImage: NetworkImage(
                ServerAssets().getAssetUrl(widget.challenge.avatar!),
              ),
              radius: 20,
            ),
            title: Text(
              widget.challenge.username!,
              style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
            ),
            subtitle: _buildChallengeText(widget.challenge.challenge!),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  widget.challenge.bet!,
                  style: const TextStyle(
                      fontWeight: FontWeight.w400, fontSize: 14),
                ),
                const SizedBox(width: 8),
                Image.asset(
                  'assets/images/challenges/bi_coin.png',
                  width: 18,
                  height: 18,
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ChallengeAmountBtn("\$10", () => _addParticipant(10)),
              ChallengeAmountBtn("\$20", () => _addParticipant(20)),
              ChallengeAmountBtn("\$30", () => _addParticipant(30)),
              ChallengeAmountBtn("Custom", () {
                showCustomInput();
              }),
            ],
          ),
          const SizedBox(height: 8),
          _buildParticipantsRow(),
        ],
      ),
    );
  }

  Widget _buildParticipantsRow() {
    if (participants.isEmpty) {
      return const Padding(
        padding: EdgeInsets.only(top: 8.0),
        child: Text(
          "No participants yet",
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return FutureBuilder<List<ProfileData>>(
      future: Future.wait(participants
          .map((p) => ServiceProvider.profileRepository.getProfileData(p.userId))),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Padding(
            padding: EdgeInsets.only(top: 8.0),
            child: Text(
              "Loading participants...",
              style: TextStyle(color: Colors.grey),
            ),
          );
        }

        final profileDataList = snapshot.data!;

        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: List.generate(participants.length, (index) {
              if (index >= profileDataList.length) return const SizedBox();

              final participant = participants[index];
              final profile = profileDataList[index];

              return GestureDetector(
                onTap: () async {
                  ProfileData userDetails = await ServiceProvider.profileRepository
                      .getProfileData(participant.userId);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => Scaffold(
                        body: ProfilePage(
                          userDetails.userId!,
                          userDetails.userName!,
                          userDetails.imageUri!,
                          userDetails.fullName!,
                        ),
                      ),
                    ),
                  );
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 6, horizontal: 10),
                  margin: const EdgeInsets.only(right: 6),
                  decoration: BoxDecoration(
                    color: index.isEven ? Colors.grey[200] : Colors.grey[300],
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    children: [
                      CircleAvatar(
                        backgroundImage: NetworkImage(profile.imageUri ?? ""),
                        radius: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        "${participant.fullName} (@${participant.username}) :  \$${participant.amount}",
                        style: const TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              );
            }),
          ),
        );
      },
    );
  }

  Widget _buildChallengeText(String text) {
    final RegExp regExp = RegExp(r'@\w+');
    final List<InlineSpan> spans = [];
    int start = 0;

    for (final match in regExp.allMatches(text)) {
      if (match.start > start) {
        spans.add(TextSpan(
          text: text.substring(start, match.start),
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Styles.textLightColor,
          ),
        ));
      }

      String username = text.substring(match.start, match.end);

      spans.add(
        WidgetSpan(
          alignment: PlaceholderAlignment.baseline,
          baseline: TextBaseline.alphabetic,
          child: FutureBuilder(
            future: ServiceProvider.profileRepository.getProfileData(username),
            builder: (context, AsyncSnapshot snapshot) {
              if (!snapshot.hasData) {
                return Text(
                  username,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Styles.primaryColor,
                  ),
                );
              }

              ProfileData details = snapshot.data as ProfileData;
              return details.userId != null
                  ? GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => Scaffold(
                              body: ProfilePage(
                                details.userId!,
                                details.userName!,
                                details.imageUri!,
                                details.fullName!,
                              ),
                            ),
                          ),
                        );
                      },
                      child: Text(
                        username,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Styles.primaryColor,
                        ),
                      ),
                    )
                  : Text(
                      username,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Styles.textLightColor,
                      ),
                    );
            },
          ),
        ),
      );

      start = match.end;
    }

    if (start < text.length) {
      spans.add(TextSpan(
        text: text.substring(start),
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Styles.textLightColor,
        ),
      ));
    }

    return RichText(text: TextSpan(children: spans));
  }
}
