import 'dart:convert';
import 'package:darve/config.dart';
import 'package:darve/mobx/AuthStore/auth_store.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/init.dart';
import 'package:http/http.dart' as http;

Future<dynamic> getUserTxHistory({int count = 20, int start = 0}) async {
  try {
    final AuthStore authStore = Darve.instance.authStore;

    final jwtToken = authStore.jwtToken;
    if (jwtToken == null || jwtToken.isEmpty) {
      throw Exception('getUserTxHistory JWT Token is missing.');
    }

    Uri uri =
        Uri.parse('${AppConfig.instance.apiUrl}/api/user/wallet/history');

    uri = uri.replace(queryParameters: {
      'count': count.toString(),
      'start': start.toString(),
    });

    final response = await http.get(
      uri,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': 'jwt=$jwtToken',
      },
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to getUserTxHistory: ${response.body}');
    }
  } catch (error) {
    ErrorsHandle().displayErrorToast(error, "getUserTxHistory");
  }
}
