import 'dart:convert';
import 'dart:io';

import 'package:darve/config.dart';
import 'package:darve/mobx/AuthStore/auth_store.dart';
import 'package:darve/mobx/UserStore/user_store.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/init.dart';
import 'package:darve/utils/interfaces/Follower.dart';
import 'package:darve/utils/interfaces/ProfileData.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class ProfileRepository {
  Future<dynamic> editProfile(String jsonBody, String? filePath) async {
    try {
      final AuthStore authStore = Darve.instance.authStore;
      final UserStore userStore = Darve.instance.userStore;

      // Check if JWT token is available
      final jwtToken = authStore.jwtToken;
      if (jwtToken == null || jwtToken.isEmpty) {
        throw Exception('JWT Token is missing.');
      }

      // Debugging logs
      debugPrint("JWT Token: $jwtToken");

      // Check if user details are available
      final username = userStore.username ?? "";
      final email = userStore.email ?? "";
      final fullName = userStore.full_name ?? "";

      // Debugging logs
      debugPrint("Username: $username, Email: $email, Full Name: $fullName");

      final uri = Uri.parse('${AppConfig.instance.apiUrl}/api/accounts/edit');
      final request = http.MultipartRequest('POST', uri)
        ..headers['Cookie'] = 'jwt=$jwtToken'
        ..headers['Content-Type'] = 'application/json'
        ..headers['Accept'] = 'application/json'
        ..fields['data'] = jsonBody
        ..fields['username'] = username
        ..fields['email'] = email
        ..fields['full_name'] = fullName;

      // Handle file upload if filePath is provided
      if (filePath != null && filePath.isNotEmpty) {
        try {
          final file = await http.MultipartFile.fromPath('image_url', filePath);
          request.files.add(file);
        } catch (fileError) {
          debugPrint("File upload error: $fileError");
        }
      }

      final response = await request.send();

      if (response.statusCode == 200) {
        final responseString = await response.stream.bytesToString();
        debugPrint("Profile updated successfully: $responseString");

        // Ensure username exists before calling getProfileData
        if (userStore.username == null) {
          throw Exception("Username is missing when fetching profile data.");
        }

        final updatedUserDetails = await getProfileData(userStore.username!);

        // Debugging logs
        debugPrint("Updated User Details: $updatedUserDetails");

        // Update userStore with new details
        userStore.setUserStore(
          updatedUserDetails.userName ?? "",
          updatedUserDetails.userId ?? "",
          updatedUserDetails.imageUri ?? "",
          updatedUserDetails.fullName ?? "",
          updatedUserDetails.bio?.toString() ?? "",
          updatedUserDetails.emailId ?? "",
        );

        return responseString;
      } else {
        final errorData = await response.stream.bytesToString();
        throw Exception('Failed to edit profile: $errorData');
      }
    } catch (error) {
      debugPrint("Error in editProfile: $error");
      ErrorsHandle().displayErrorToast(error, "editProfile");
      return null;
    }
  }

  Future<ProfileData> getProfileData(String username) async {
    try {
      if (username.startsWith("@")) {
        username = username.split("@")[1];
      }

      final response = await http.get(
        Uri.parse('${AppConfig.instance.apiUrl}/u/$username'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData != null) {
          return ProfileData.fromJson(responseData);
        }
      } else {
        throw Exception('FAILED: ${response.body}');
      }
    } catch (error) {
      ErrorsHandle().displayErrorToast(error, "getProfileData+$username");
      return ProfileData.empty();
    }
    return ProfileData.empty();
  }

  Future<dynamic> followUser(String userId) async {
    try {
      final AuthStore authStore = Darve.instance.authStore;

      final jwtToken = authStore.jwtToken;
      if (jwtToken == null || jwtToken.isEmpty) {
        throw Exception('followUser JWT Token is missing.');
      }

      final response = await http.post(
        Uri.parse('${AppConfig.instance.apiUrl}/api/follow/$userId'),
        headers: {
          'Accept': 'application/json',
          'Cookie': 'jwt=$jwtToken',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData;
      } else {
        throw Exception('Failed to follow user: ${response.body}');
      }
    } catch (error) {
      ErrorsHandle().displayErrorToast(error, "followUser");
    }
  }

  Future<List<Follower>> getFollowers(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.instance.apiUrl}/api/user/$userId/followers'),
        headers: {
          'Accept': 'application/json',
        },
      );
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        // Assuming responseData['list'] is a List of dynamic objects.
        List<Follower> followers = (responseData['items'] as List)
            .map((item) => Follower(
                  username: item['username'],
                  name: item['name'],
                  imageUrl: item['image_url'],
                ))
            .toList();
        return followers;
      } else {
        throw Exception('FAILED: ${response.body}');
      }
    } catch (error) {
      ErrorsHandle().displayErrorToast(error, "getFollowers");
      return [];
    }
  }

  Future<List<Follower>> getFollowing(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.instance.apiUrl}/api/user/$userId/following'),
        headers: {
          'Accept': 'application/json',
        },
      );
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        // Assuming responseData['list'] is a List of dynamic objects.
        List<Follower> following = (responseData['items'] as List)
            .map((item) => Follower(
                  username: item['username'],
                  name: item['name'],
                  imageUrl: item['image_url'],
                ))
            .toList();
        return following;
      } else {
        throw Exception('FAILED: ${response.body}');
      }
    } catch (error) {
      ErrorsHandle().displayErrorToast(error, "getFollowing");
      return [];
    }
  }

  Future<dynamic> isFollowing(String otherUserId) async {
    try {
      final AuthStore authStore = Darve.instance.authStore;

      final jwtToken = authStore.jwtToken;
      if (jwtToken == null || jwtToken.isEmpty) {
        throw Exception('isFollowing JWT Token is missing.');
      }

      final response = await http.get(
        Uri.parse(
            '${AppConfig.instance.apiUrl}/api/user/follows/$otherUserId'),
        headers: {
          'Accept': 'application/json',
          'Cookie': 'jwt=$jwtToken',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData['success'];
      } else {
        throw Exception('Failed to check isFollowing: ${response.body}');
      }
    } catch (error) {
      ErrorsHandle().displayErrorToast(error, "isFollowing");
    }
  }

  Future<dynamic> searchUser(String userInput) async {
    try {
      final AuthStore authStore = Darve.instance.authStore;

      final jwtToken = authStore.jwtToken;
      if (jwtToken == null || jwtToken.isEmpty) {
        throw Exception('searchUser JWT Token is missing.');
      }

      final response = await http.post(
        Uri.parse('${AppConfig.instance.apiUrl}/api/user/search'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Cookie': 'jwt=$jwtToken',
        },
        body: json.encode({
          'query': userInput,
        }),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData['items'];
      } else {
        throw Exception('searchUser error: ${response.body}');
      }
    } catch (error) {
      ErrorsHandle().displayErrorToast(error, "searchUser");
      return [];
    }
  }

  Future<dynamic> unfollowUser(String userId) async {
    try {
      final AuthStore authStore = Darve.instance.authStore;

      final jwtToken = authStore.jwtToken;
      if (jwtToken == null || jwtToken.isEmpty) {
        throw Exception('JWT Token is missing.');
      }

      final response = await http.delete(
        Uri.parse('${AppConfig.instance.apiUrl}/api/follow/$userId'),
        headers: {
          'Accept': 'application/json',
          'Cookie': 'jwt=$jwtToken',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData;
      } else {
        throw Exception('Failed to unfollowUser: ${response.body}');
      }
    } catch (error) {
      debugPrint('Error during followUser() CALL: $error');
    }
  }

  Future<void> uploadFile(String filePath) async {
    final uri = Uri.parse('${AppConfig.instance.apiUrl}/api/upload');
    final file = File(filePath);

    if (!file.existsSync()) {
      debugPrint('File does not exist at path: $filePath');
      return;
    }

    try {
      final request = http.MultipartRequest('POST', uri);
      request.headers.addAll({
        'Accept': 'application/json',
      });

      final fileBytes = await file.readAsBytes();
      final fileName = file.uri.pathSegments.last;

      request.files.add(http.MultipartFile.fromBytes(
        'file',
        fileBytes,
        filename: fileName,
      ));

      final response = await request.send();

      if (response.statusCode == 200) {
        debugPrint('File uploaded successfully');
        final responseData = await response.stream.bytesToString();
        debugPrint('Response: $responseData');
      } else {
        debugPrint('File upload failed with status: ${response.statusCode}');
        final errorData = await response.stream.bytesToString();
        debugPrint('Error: $errorData');
      }
    } catch (e) {
      debugPrint('An error occurred during file upload: $e');
    }
  }
}
