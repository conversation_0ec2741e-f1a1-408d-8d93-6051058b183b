import 'dart:io';
import 'package:darve/config.dart';
import 'package:darve/mobx/AuthStore/auth_store.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/init.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

Future<dynamic> deliverTaskRequest(
    String taskId, String filePath, String postId) async {
  try {
    final AuthStore authStore = Darve.instance.authStore;
    final jwtToken = authStore.jwtToken;

    if (jwtToken == null || jwtToken.isEmpty) {
      throw Exception('createPost JWT Token is missing.');
    }

    final uri = Uri.parse(
        '${AppConfig.instance.apiUrl}/api/task_request/$taskId/deliver');
    final request = http.MultipartRequest('POST', uri)
      ..headers['Cookie'] = 'jwt=$jwtToken'
      ..headers['Accept'] = 'application/json'
      ..fields['post_id'] = postId;

    if (filePath.isNotEmpty) {
      final file = File(filePath);
      final fileBytes = await file.readAsBytes();
      final fileName = file.uri.pathSegments.last;
      final multipartFile = http.MultipartFile.fromBytes(
        'file_1',
        fileBytes,
        filename: fileName,
      );
      request.files.add(multipartFile);
    }

    final response = await request.send();

    if (response.statusCode == 200 || response.statusCode == 201) {
      final responseData = await response.stream.bytesToString();
      debugPrint(responseData.toString());
      return responseData;
    } else {
      final errorData = await response.stream.bytesToString();
      throw Exception('Failed to deliverTaskRequest: $errorData');
    }
  } catch (error) {
    ErrorsHandle().displayErrorToast(error, "deliverTaskRequest");
    return null;
  }
}
