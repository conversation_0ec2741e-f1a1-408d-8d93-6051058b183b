import 'dart:convert';
import 'package:darve/config.dart';
import 'package:darve/mobx/AuthStore/auth_store.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/init.dart';
import 'package:darve/utils/interfaces/Challenge.dart';
import 'package:http/http.dart' as http;

Future<List<Challenge>> getGivenTaskRequestsForPost(String postId) async {
  try {
    final AuthStore authStore = Darve.instance.authStore;

    final jwtToken = authStore.jwtToken;
    if (jwtToken == null || jwtToken.isEmpty) {
      throw Exception('JWT Token is missing.');
    }

    Uri uri = Uri.parse(
        '${AppConfig.instance.apiUrl}/api/task_request/list/post/$postId');

    final response = await http.get(
      uri,
      headers: {
        'Accept': 'application/json',
        'Cookie': 'jwt=$jwtToken',
      },
    );

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      List<Challenge> parsedResponse = [];
      for (var response in responseData) {
        parsedResponse.add(Challenge.fromJson(response));
      }
      return parsedResponse;
    } else {
      throw Exception('Failed to fetch given task requests: ${response.body}');
    }
  } catch (error) {
    ErrorsHandle().displayErrorToast(error, "getGivenTaskRequestsForPost");
    return [];
  }
}
