import 'dart:convert';
import 'package:darve/config.dart';
import 'package:darve/mobx/AuthStore/auth_store.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/init.dart';
import 'package:http/http.dart' as http;

Future<dynamic> createTaskRequest({
  required String toUserId,
  required String content,
  required String postId,
  required double offerAmount,
}) async {
  try {
    final AuthStore authStore = Darve.instance.authStore;

    final jwtToken = authStore.jwtToken;
    if (jwtToken == null || jwtToken.isEmpty) {
      throw Exception('JWT Token is missing.');
    }

    final body = json.encode({
      'to_user': toUserId,
      'content': content,
      'post_id': postId,
      'offer_amount': offerAmount.toInt(),
    });

    final response = await http.post(
      Uri.parse('${AppConfig.instance.apiUrl}/api/task_request'),
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'jwt=$jwtToken',
      },
      body: body,
    );

    if (response.statusCode == 200) {
      return response.body;
    } else {
      throw Exception('Failed to create task request: ${response.body}');
    }
  } catch (error) {
    ErrorsHandle().displayErrorToast(error, "createTaskRequest");
  }
}
