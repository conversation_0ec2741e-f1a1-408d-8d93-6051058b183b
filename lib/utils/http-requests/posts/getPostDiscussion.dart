import 'dart:convert';
import 'package:darve/config.dart';
import 'package:darve/mobx/AuthStore/auth_store.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/http-requests/profile/profile_repository.dart';
import 'package:darve/utils/init.dart';
import 'package:darve/utils/interfaces/ShortenedComment.dart';
import 'package:http/http.dart' as http;

Future<List<ShortenedComment>> getPostDiscussions(
    String discussionId, String postId) async {
  try {
    final AuthStore authStore = Darve.instance.authStore;

    final jwtToken = authStore.jwtToken;
    if (jwtToken == null || jwtToken.isEmpty) {
      throw Exception('getPostDiscussions JWT Token is missing.');
    }

    final response = await http.get(
      Uri.parse(
          '${AppConfig.instance.apiUrl}/api/discussion/$discussionId/post/$postId/replies'),
      headers: {
        'Accept': 'application/json',
        'Cookie': 'jwt=$jwtToken',
      },
    );

    if (response.statusCode == 200) {
      List<ShortenedComment> comments = [];

      // username,comment,likes,avatar
      for (var i = 0; i < json.decode(response.body)['replies'].length; i++) {
        var comment = json.decode(response.body)['replies'][i];
        var userDetails =
            await ProfileRepository().getProfileData(comment['username']);
        comments.add(ShortenedComment(
            avatar: userDetails.imageUri,
            username: comment['username'],
            comment: comment['title'],
            likes: '0' // TODO: remove later
            ));
      }

      return comments;
    } else {
      throw Exception('getPostDiscussions: ${response.body} for $discussionId');
    }
  } catch (error) {
    ErrorsHandle().displayErrorToast(error, "getPostDiscussions");
    return [];
  }
}
