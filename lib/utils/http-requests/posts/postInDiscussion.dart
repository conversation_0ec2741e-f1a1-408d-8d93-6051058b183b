import 'dart:convert';
import 'package:darve/config.dart';
import 'package:darve/mobx/AuthStore/auth_store.dart';
import 'package:darve/pages/utility.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/init.dart';
import 'package:http/http.dart' as http;

Future<bool> postInDiscussion(
    String discussionId, String postUri, String content) async {
  try {
    final AuthStore authStore = Darve.instance.authStore;
    final jwtToken = authStore.jwtToken;

    if (jwtToken == null || jwtToken.isEmpty) {
      throw Exception('postInDiscussion JWT Token is missing.');
    }

    final postTitle = generateRandomTitle(32);

    final uri = Uri.parse(
        '${AppConfig.instance.apiUrl}/api/discussion/$discussionId/post/$postUri/reply');
    final headers = {
      'Cookie': 'jwt=$jwtToken',
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    final body = jsonEncode({
      'title': content,
      'content': postTitle,
    });

    final response = await http.post(uri, headers: headers, body: body);

    if (response.statusCode == 200 || response.statusCode == 201) {
      return json.decode(response.body)["success"];
    } else {
      final errorData = response.body;
      throw Exception('postInDiscussion Failed to post comment: $errorData');
    }
  } catch (error) {
    ErrorsHandle().displayErrorToast(error, "postInDiscussion");
    return false;
  }
}
