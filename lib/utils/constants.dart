class DarveConstants {
  // final String backendUrl = "http://*************:8080";
  // final String backendUrl = "https://darve.reefscan.info";
  // final String backendUrl = "http://localhost:8080";
}

enum WalletType { deposit, withdraw, transfer }

// Extension to map enum values to strings
extension WalletTypeExtension on WalletType {
  String get value {
    switch (this) {
      case WalletType.deposit:
        return 'Deposit';
      case WalletType.withdraw:
        return 'Withdraw';
      case WalletType.transfer:
        return 'Transfer';
    }
  }
}

enum ChallengeType { all, requested, accepted, rejected, delivered }

extension ChallengeTypeExtension on ChallengeType {
  String get value {
    switch (this) {
      case ChallengeType.all:
        return 'All';
      case ChallengeType.requested:
        return 'Requested';
      case ChallengeType.accepted:
        return 'Accepted';
      case ChallengeType.rejected:
        return 'Rejected';
      case ChallengeType.delivered:
        return 'Delivered';
    }
  }
}

enum RequestCacheType { history, chatList, discussion }

extension RequestCacheTypeExtension on RequestCacheType {
  String get value {
    switch (this) {
      case RequestCacheType.history:
        return 'history_cache';
      case RequestCacheType.chatList:
        return 'chat_list_cache';
      case RequestCacheType.discussion:
        return 'chat_discussion_cache';
    }
  }
}
