import 'package:darve/main.dart';
import 'package:flutter/material.dart';

class ErrorsHandle {
  void displayErrorToast(dynamic error, [String? functionName]) {
    debugPrint("ERR===$error functionName==$functionName");
    final context = navigatorKey.currentContext!;

    var errorMessage = functionName == "followUser"
        ? "Already following the user!"
        : functionName == "urlLaunchError"
            ? error.toString()
            : error.toString().contains("The provided JWT")
                ? "Session Expired! Log in again"
                : error.toString().contains("Min 5 characters")
                    ? "Content length too short, need atleast 5 characters"
                    : error.toString();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: DefaultTextStyle(
          style: const TextStyle(fontSize: 12.0, color: Colors.white),
          child: Center(child: Text(errorMessage)),
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.only(top: 10, left: 10, bottom: 10, right: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
