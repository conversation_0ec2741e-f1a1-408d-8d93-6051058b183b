import 'dart:convert';

import 'package:darve/utils/constants.dart';
import 'package:shared_preferences/shared_preferences.dart';

class RequestCache {
  RequestCache._internal();

  // Singleton instance
  static final RequestCache _instance = RequestCache._internal();

  // SharedPreferences instance
  SharedPreferences? _prefs;

  // Public factory to access the singleton
  static Future<RequestCache> getInstance() async {
    // Initialize SharedPreferences if not already initialized
    if (_instance._prefs == null) {
      _instance._prefs = await SharedPreferences.getInstance();
    }
    return _instance;
  }

  void setWalletHistoryCache(Map<String, dynamic> value) {
    String jsonString = jsonEncode(value);
    _prefs?.setString(RequestCacheType.history.value, jsonString);
  }

  Map<String, dynamic>? getWalletHistoryCache() {
    var value = _prefs?.getString(RequestCacheType.history.value);
    if (value == null) {
      return null;
    }
    return jsonDecode(value);
  }

  void setChatListCache(Map<String, dynamic> value) {
    String jsonString = jsonEncode(value);
    _prefs?.setString(RequestCacheType.chatList.value, jsonString);
  }

  Map<String, dynamic>? getChatListCache() {
    var value = _prefs?.getString(RequestCacheType.chatList.value);
    if (value == null) {
      return null;
    }
    return jsonDecode(value);
  }

  void setDiscussionCache(
      List<Map<String, dynamic>> value, String discussionId) {
    String jsonString = jsonEncode(value);
    _prefs?.setString(
        '${RequestCacheType.discussion.value}_$discussionId', jsonString);
  }

  List<Map<String, dynamic>>? getDiscussionCache(String discussionId) {
    var value =
        _prefs?.getString('${RequestCacheType.discussion.value}_$discussionId');
    if (value == null) {
      return null;
    }
    return (jsonDecode(value) as List)
        .map((item) => item as Map<String, dynamic>)
        .toList();
  }

  void clearCache() {
    _prefs?.getKeys().forEach((key) {
      if (key.startsWith(RequestCacheType.discussion.value)) {
        _prefs?.remove(key);
      }
      if (key == RequestCacheType.chatList.value) {
        _prefs?.remove(key);
      }
      if (key == RequestCacheType.history.value) {
        _prefs?.remove(key);
      }
    });
  }
}
