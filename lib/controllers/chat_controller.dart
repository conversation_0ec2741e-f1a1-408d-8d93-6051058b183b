import 'dart:async';
import 'dart:convert';

import 'package:darve/helpers/discussion_helper.dart';
import 'package:darve/mobx/UserStore/user_store.dart';
import 'package:darve/api/repositories/chat_repository.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:darve/utils/http-requests/request_cache.dart';
import 'package:darve/utils/init.dart';
import 'package:darve/utils/interfaces/chat_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChatController extends GetxController {
  final ChatRepository chatListRepository = ServiceProvider.chatRepository;
  final UserStore userStore = Darve.instance.userStore;
  RxList<Chat> chats = <Chat>[].obs;
  StreamSubscription? _sseSubscription;
  var isLoading = true.obs;
  RequestCache? requestCache;
  RxString? discussionId;

  @override
  void onInit() async {
    super.onInit();
    requestCache ??= await RequestCache.getInstance();
    // requestCache!.clearCache();
    await loadData();
  }

  void cacheLastTenMessages() {
    if (chats.isNotEmpty) {
      // Cache the last 10 messages
      var messgesCountToCaceh = chats.length > 10 ? 10 : chats.length;
      var lastTen = chats
          .sublist(chats.length - messgesCountToCaceh, chats.length)
          .map((e) => e.toJson())
          .toList();
      requestCache!.setDiscussionCache(lastTen, discussionId!.value);
    }
  }

  void restoreLastTenMessages() {
    var lastTen = requestCache!.getDiscussionCache(discussionId!.value);
    if (lastTen != null) {
      chats.value = lastTen.map((e) => Chat.fromJson(e)).toList();
    }
  }

  Future<void> loadData() async {
    if (discussionId == null) {
      debugPrint("Chat ID is null. Cannot load data.");
      return;
    }
    isLoading.value = true;
    restoreLastTenMessages();
    _sseSubscription ??= chatListRepository
        .getChatDiscussionByIdSse(discussionId!.value)
        .listen((event) {
      if (event != null) {
        try {
          final lines =
              (event.split('\n') as List).map((e) => e.toString()).toList();
          final jsonString = lines.firstWhere(
            (line) => line.trim().startsWith('{'),
            orElse: () => '',
          );

          if (jsonString.isNotEmpty) {
            final parsedEvent = jsonDecode(jsonString);
            var chat = Chat(
              inbound: parsedEvent['created_by_name'] != userStore.username,
              body: parsedEvent['content'],
              time: DateTime.parse(parsedEvent['r_created'])
                  .millisecondsSinceEpoch,
              type: "text",
            );
            chats.add(chat);
            cacheLastTenMessages();
          } else {
            debugPrint("No valid JSON found in the event string: $event");
          }
        } catch (e) {
          debugPrint("Error parsing event: $e");
        }
      }
    }, onError: (error) {
      debugPrint('Error occurred: $error');
    }, onDone: () {
      debugPrint('Stream closed.');
    });
    getChatDiscussionById();
  }

  Future<void> getChatDiscussionById() async {
    if (discussionId == null) {
      debugPrint("Chat ID is null. Cannot load data.");
      return;
    }
    var chatDiscussion =
        await chatListRepository.getChatDiscussionById(discussionId!.value);

    List<Chat> fetchedChats = [];
    for (var e in chatDiscussion) {
      fetchedChats.add(
        Chat(
          inbound: e['created_by_name'] != userStore.username,
          body: e['content'],
          time: DateTime.parse(e['r_created']).millisecondsSinceEpoch,
          type: "text",
        ),
      );
    }
    chats.value = fetchedChats;
    isLoading.value = false;
    cacheLastTenMessages();
  }

  Future<String?> createChatWithUserId(String otherUserId) async {
    try {
      final response =
          await chatListRepository.createChatWithUserId(otherUserId);
      if (response != null) {
        var discussionId =
            DiscussionIdHelper().getDiscussionId(response["discussion"]);
        return discussionId;
      }
    } catch (error) {
      debugPrint("Error creating chat with user ID $otherUserId: $error");
    }
    return null;
  }

  Future<void> postMessageInChat(String content) async {
    if (discussionId == null) {
      debugPrint("Chat ID is null. Cannot load data.");
      return;
    }
    try {
      final response = await chatListRepository.postMessageInChat(
          discussionId!.value, content);
      if (response != null) {
        debugPrint("Message posted successfully: $response");
      }
    } catch (error) {
      debugPrint("Error posting message in chat: $error");
    }
  }
}
