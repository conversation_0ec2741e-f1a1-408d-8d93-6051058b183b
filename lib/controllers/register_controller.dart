

import 'package:darve/controllers/auth_controller.dart';
import 'package:darve/mobx/UserStore/user_store.dart';
import 'package:darve/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/utils/init.dart';
import 'package:get/get.dart';

class RegisterController extends GetxController {
  RxList<String> errorsArray = <String>[].obs;

  String enteredEmail = '';
  String enteredFullName = '';
  String username = '';
  String enteredPassword1 = '';
  String enteredPassword2 = '';

  String filepath = '';

  RxBool isPasswordVisible = false.obs;
  RxBool isFirstStage = true.obs;
  RxBool isCheckBoxClicked = false.obs;

  List<String> days = List.generate(31, (index) => (index + 1).toString());
  List<String> months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "June",
    "July",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec"
  ];
  List<String> years =
      List.generate(100, (index) => (DateTime.now().year - index).toString());

  RxString selectedDay = '1'.obs;
  RxString selectedMonth = 'Jan'.obs;
  RxString selectedYear = DateTime.now().year.toString().obs;

  final UserStore userStore = Darve.instance.userStore;
  AuthController auth = Get.find<AuthController>();
  late final AuthService authService;

  @override
  void onInit() {
    super.onInit();
    authService = AuthProvider.auth;
  }

  void register() async {
    try {
      // Use new auth service for registration
      await authService.register(
        username: username,
        password: enteredPassword1,
        email: enteredEmail,
        name: enteredFullName,
        imageUri: 'https://avatars.githubusercontent.com/u/185073648?s=200&v=4',
      );

      // Handle errors
      if (authService.error != null) {
        errorsArray.add(authService.error!.message);
        authService.clearError();
        return;
      }

      // If successful, navigate back
      if (authService.isLoggedIn) {
        Get.back();
      }
    } catch (error) {
      errorsArray.add(error.toString());
      rethrow;
    }
  }
}
