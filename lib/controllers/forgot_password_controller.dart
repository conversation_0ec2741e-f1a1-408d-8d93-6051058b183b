import 'package:darve/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/services/auth/models/auth_error.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ForgotPasswordController extends GetxController {
  RxBool isEmailSent = false.obs;
  String usernameOrEmail = '';
  late final AuthService authService;

  @override
  void onInit() {
    super.onInit();
    authService = AuthProvider.auth;
  }

  // Computed property to get loading state from auth service
  bool get isLoading => authService.loadingState == AuthLoadingState.loading;

  // Computed property to get error from auth service
  AuthError? get error => authService.error;

  Future<void> forgotPassword() async {
    try {
      await authService.forgotPassword(usernameOrEmail);

      // Check for errors
      if (authService.error != null) {
        // Error will be handled by the UI observing the auth service
        return;
      }

      // Success
      usernameOrEmail = '';
      isEmailSent.value = true;
    } catch (e) {
      debugPrint("Error sending reset link: $e");
    }
  }

  void clearError() {
    authService.clearError();
  }
}
