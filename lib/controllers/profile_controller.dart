import 'package:darve/mobx/UserStore/user_store.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:darve/utils/init.dart';
import 'package:darve/utils/interfaces/Follower.dart';
import 'package:darve/utils/interfaces/Post.dart';
import 'package:darve/utils/interfaces/ProfileData.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileController extends GetxController {
  final UserStore userStore = Darve.instance.userStore;
  Rx<ProfileData> userProfile = ProfileData.empty().obs;
  RxBool isMe = true.obs;
  RxList posts = <Post>[].obs;
  RxBool loadingPosts = true.obs;
  RxList<Follower> followers = <Follower>[].obs;
  String userName;
  String userId;
  RxString content = "content".obs;
  ProfileController({required this.userName, required this.userId});

  Future<bool> profileDataReload() async {
    try {
      loadingPosts.value = true;
      var res = await ServiceProvider.profileRepository.getProfileData(userName);

      isMe.value = userStore.username == userName;
      userProfile.value = res;
      ServiceProvider.postsRepository.getPosts(userName).then((val) {
        loadingPosts.value = false;
        posts.value = val;
      });
      ServiceProvider.profileRepository.getFollowers(userId).then((val) {
        followers.value = val;
      });
      return true;
    } catch (e) {
      debugPrint("e===$e");
      return false;
    }
  }

  Future<void> makePost(String? pickedFilePath) async {
    if (pickedFilePath != null) {
      ServiceProvider.postsRepository.createPost(content.value, () async {
        debugPrint("executed");
        await profileDataReload();
        return true;
      }, pickedFilePath);
    } else {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
      );
      if (result != null && result.files.single.path != null) {
        ServiceProvider.postsRepository.createPost(content.value, () async {
          debugPrint("executed");
          await profileDataReload();
          return true;
        }, result.files.single.path!);
      } else {
        debugPrint("No file selected or file path is null.");
      }
    }
  }
}
