import 'dart:convert';

import 'package:darve/api/models/user_notification_model.dart';
import 'package:darve/api/repositories/notification_repository.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:darve/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/utils/interfaces/Notification.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NotificationsController extends GetxController {
  var isLoading = true.obs;
  var hasError = false.obs;
  RxList<DarveNotification> notifications = <DarveNotification>[].obs;

  late final AuthService authService;
  late final NotificationRepository notificationRepository;

  @override
  void onInit() {
    super.onInit();
    authService = AuthProvider.auth;
    notificationRepository = ServiceProvider.notificationRepository;
    loadData();
  }

  // Adapter method to convert UserNotificationModel to DarveNotification
  // This preserves existing business logic and UI components
  DarveNotification _adaptUserNotificationToDarveNotification(UserNotificationModel userNotification) {
    String type;
    Object? value;

    // Map UserNotificationEvent enum to DarveNotification string constants
    switch (userNotification.event) {
      case UserNotificationEvent.userFollowAdded:
        type = DarveNotification.userFollowAdded;
        // Extract username from metadata if available
        if (userNotification.metadata != null && userNotification.metadata is Map) {
          final metadata = userNotification.metadata as Map<String, dynamic>;
          value = UserFollowAdded(
            username: metadata['username'] ?? userNotification.createdBy,
            followedUser: metadata['followedUser'] ?? authService.user?.username ?? '',
          );
        } else {
          value = UserFollowAdded(
            username: userNotification.createdBy,
            followedUser: authService.user?.username ?? '',
          );
        }
        break;
      case UserNotificationEvent.userTaskRequestCreated:
        type = DarveNotification.userTaskRequestCreated;
        if (userNotification.metadata != null && userNotification.metadata is Map) {
          final metadata = userNotification.metadata as Map<String, dynamic>;
          value = UserTaskRequestCreated(
            taskId: metadata['taskId'] ?? userNotification.id,
            fromUser: metadata['fromUser'] ?? userNotification.createdBy,
            toUser: metadata['toUser'] ?? authService.user?.id ?? '',
          );
        } else {
          value = UserTaskRequestCreated(
            taskId: userNotification.id,
            fromUser: userNotification.createdBy,
            toUser: authService.user?.id ?? '',
          );
        }
        break;
      case UserNotificationEvent.userChatMessage:
        type = DarveNotification.userChatMessage;
        value = userNotification.metadata;
        break;
      case UserNotificationEvent.userTaskRequestReceived:
        type = DarveNotification.userTaskRequestReceived;
        value = userNotification.metadata;
        break;
      case UserNotificationEvent.userTaskRequestDelivered:
        type = DarveNotification.userTaskRequestDelivered;
        value = userNotification.metadata;
        break;
      case UserNotificationEvent.userCommunityPost:
        type = DarveNotification.userCommunityPost;
        value = userNotification.metadata;
        break;
      case UserNotificationEvent.userBalanceUpdate:
        type = DarveNotification.userBalanceUpdate;
        value = userNotification.metadata;
        break;
      default:
        type = userNotification.event.name;
        value = userNotification.metadata;
        break;
    }

    return DarveNotification.fromDetails(userNotification.id, type, value);
  }

  void loadData() async {
    isLoading.value = true;
    try {
      // Get notifications using new NotificationRepository
      final userNotifications = await notificationRepository.get();

      // Convert UserNotificationModel list to DarveNotification list
      notifications.value = userNotifications
          .map((userNotif) => _adaptUserNotificationToDarveNotification(userNotif))
          .toList();

      // Listen to SSE stream for real-time notifications
      notificationRepository.getSse().listen((userNotification) {
        debugPrint("Received new notification: ${userNotification.title}");

        // Convert and add new notification to the beginning of the list
        final darveNotification = _adaptUserNotificationToDarveNotification(userNotification);
        notifications.insert(0, darveNotification);

      }, onError: (error) {
        debugPrint('SSE Error occurred: $error');
      }, onDone: () {
        debugPrint('SSE Stream closed.');
      });
    } catch (e) {
      debugPrint('Error loading notifications: $e');
      hasError.value = true;
    } finally {
      isLoading.value = false;
    }
  }
}
