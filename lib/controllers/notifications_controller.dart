import 'dart:convert';

import 'package:darve/api/repositories/notification_repository.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:darve/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/utils/interfaces/Notification.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NotificationsController extends GetxController {
  var isLoading = true.obs;
  var hasError = false.obs;
  RxList<DarveNotification> notifications = <DarveNotification>[].obs;

  late final AuthService authService;
  late final NotificationRepository notificationRepository;

  @override
  void onInit() {
    super.onInit();
    authService = AuthProvider.auth;
    notificationRepository = ServiceProvider.notificationRepository;
    loadData();
  }



  void loadData() async {
    isLoading.value = true;
    try {
      // Get notifications using repository's adapter method
      notifications.value = await notificationRepository.getNotifications();

      // Listen to SSE stream for real-time notifications using repository's adapter method
      notificationRepository.getNotificationsSse().listen((darveNotification) {
        debugPrint("Received new notification: ${darveNotification.id}");

        // Add new notification to the beginning of the list
        notifications.insert(0, darveNotification);

      }, onError: (error) {
        debugPrint('SSE Error occurred: $error');
      }, onDone: () {
        debugPrint('SSE Stream closed.');
      });
    } catch (e) {
      debugPrint('Error loading notifications: $e');
      hasError.value = true;
    } finally {
      isLoading.value = false;
    }
  }
}
