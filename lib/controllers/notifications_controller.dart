import 'dart:convert';

import 'package:darve/api/repositories/notifications_repository.dart';
import 'package:darve/mobx/UserStore/user_store.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:darve/utils/init.dart';
import 'package:darve/utils/interfaces/Notification.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NotificationsController extends GetxController {
  var isLoading = true.obs;
  var hasError = false.obs;
  RxList<DarveNotification> notifications = <DarveNotification>[].obs;
  final UserStore userStore = Darve.instance.userStore;
  NotificationsRepository notificationsRepository = ServiceProvider.notificationsRepository;

  @override
  void onInit() {
    super.onInit();
    loadData();
  }

  void loadData() async {
    isLoading.value = true;
    try {
      notifications.value = await notificationsRepository.getNotifications();

      notificationsRepository.getNotificationsSse().listen((event) {
        debugPrint("Listening notifications $event");
        if (event != null) {
          try {
            final lines =
                (event.split('\n') as List).map((e) => e.toString()).toList();
            final jsonString = lines.firstWhere(
              (line) => line.trim().startsWith('{'),
              orElse: () => '',
            );

            if (jsonString.isNotEmpty) {
              final parsedEvent = jsonDecode(jsonString);
              debugPrint("parsedEvent===$parsedEvent");
            } else {
              debugPrint("No valid JSON found in the event string: $event");
            }
          } catch (e) {
            debugPrint("Error parsing event: $e");
          }
        }
      }, onError: (error) {
        debugPrint('Error occurred: $error');
      }, onDone: () {
        debugPrint('Stream closed.');
      });
    } catch (e) {
      hasError.value = true;
    } finally {
      isLoading.value = false;
    }
  }
}
