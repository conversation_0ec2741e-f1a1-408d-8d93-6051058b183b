import 'dart:convert';

import 'package:darve/api/repositories/user_notifications.dart';
import 'package:darve/api/models/user_notification_model.dart';
import 'package:darve/providers/auth_provider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NotificationsController extends GetxController {
  var isLoading = true.obs;
  var hasError = false.obs;
  RxList<UserNotificationModel> notifications = <UserNotificationModel>[].obs;

  // Get UserNotificationsApi instance with current auth token
  UserNotificationsApi get _notificationsApi {
    final authService = AuthProvider.auth;
    final token = authService.authState.value.token;
    if (token == null) throw Exception('No auth token available');
    return UserNotificationsApi(jwtToken: token);
  }

  @override
  void onInit() {
    super.onInit();
    loadData();
  }

  void loadData() async {
    isLoading.value = true;
    try {
      // Get notifications using UserNotificationsApi
      notifications.value = await _notificationsApi.get(null);

      // Listen to SSE stream for real-time notifications
      _notificationsApi.getSse().listen((notification) {
        debugPrint("Received new notification: ${notification.title}");
        // Add new notification to the beginning of the list
        notifications.insert(0, notification);
      }, onError: (error) {
        debugPrint('SSE Error occurred: $error');
      }, onDone: () {
        debugPrint('SSE Stream closed.');
      });
    } catch (e) {
      debugPrint('Error loading notifications: $e');
      hasError.value = true;
    } finally {
      isLoading.value = false;
    }
  }

  // Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await _notificationsApi.read(notificationId);
      // Update local state
      final index = notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        final notification = notifications[index];
        notifications[index] = UserNotificationModel(
          id: notification.id,
          event: notification.event,
          title: notification.title,
          isRead: true,
          createdBy: notification.createdBy,
          metadata: notification.metadata,
        );
      }
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      await _notificationsApi.readAll();
      // Update local state
      notifications.value = notifications.map((notification) =>
        UserNotificationModel(
          id: notification.id,
          event: notification.event,
          title: notification.title,
          isRead: true,
          createdBy: notification.createdBy,
          metadata: notification.metadata,
        )
      ).toList();
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
    }
  }

  // Get unread notifications count
  Future<num> getUnreadCount() async {
    try {
      return await _notificationsApi.getCountOfUnread();
    } catch (e) {
      debugPrint('Error getting unread count: $e');
      return 0;
    }
  }
}
