import 'dart:async';

import 'package:darve/helpers/discussion_helper.dart';
import 'package:darve/helpers/user_helper.dart';
import 'package:darve/mobx/UserStore/user_store.dart';
import 'package:darve/api/repositories/chat_repository.dart';
import 'package:darve/providers/service_provider.dart';
import 'package:darve/utils/http-requests/request_cache.dart';
import 'package:darve/utils/init.dart';
import 'package:darve/utils/interfaces/chat_storage.dart';
import 'package:get/get.dart';

class ChatListController extends GetxController {
  final ChatRepository chatListRepository = ServiceProvider.chatRepository;
  final UserStore userStore = Darve.instance.userStore;
  var chatList = <ChatList>[].obs;
  var isLoading = true.obs;
  RequestCache? requestCache;
  RxString searchQuery = ''.obs;

  StreamSubscription? _chatSubscription;

  void loadData() {
    isLoading = true.obs;
    searchQuery = ''.obs;
    _chatSubscription ??= chatListRepository.getChatListSse().listen((val) {
      getChats();
    });
    getChats();
  }

  RxList<ChatList> get filteredChats => chatList
      .where(
          (chat) => chat.id.toLowerCase().contains(searchQuery.toLowerCase()))
      .toList()
      .obs;

  Future<void> getChats() async {
    requestCache ??= await RequestCache.getInstance();
    var cache = requestCache!.getChatListCache();
    if (cache != null) {
      await parseChats(cache);
    }
    var val = await chatListRepository.getChatsList();
    if (val != null) {
      requestCache!.setChatListCache(val);
      await parseChats(val);
    }
    isLoading.value = false;
  }

  Future<void> parseChats(Map<String, dynamic> responseMap) async {
    List<ChatList> localChats = [];
    for (var i = 0; i < responseMap['discussions'].length; i++) {
      var d = responseMap['discussions'][i];
      var userDetails = await ServiceProvider.profileRepository.getProfileData(UserIdHelper()
          .getOtherUserIds(d['chat_room_user_ids'], userStore.userId!)!
          .first);
      if (userDetails.userId != null) {
        localChats.add(ChatList(
            id: DiscussionIdHelper().getDiscussionId(d),
            title: d['title'],
            userIds: userDetails.userId!,
            avatarUrl: userDetails.imageUri!,
            fullName: userDetails.fullName!,
            isOnline: false,
            userName: userDetails.userName!,
            lastMessage: d['latest_post']['content'],
            timestamp: '10:30 AM' // TODO: hardcoded
            ));
      }
    }
    chatList.value = localChats;
  }

  @override
  void dispose() {
    _chatSubscription?.cancel();
    super.dispose();
  }
}
