import 'package:darve/services/http/http_service.dart';
import 'package:darve/utils/interfaces/Post.dart';
import 'package:dio/dio.dart';

import '../../pages/utility.dart';

class PostsRepository {
  final HttpService _dioService;

  PostsRepository(this._dioService);

  
  Future<List<Post>> getPosts(String username) async {
    final response = await _dioService.get('/api/user/$username/posts');
    
    List<Post> allPosts = [];
    for (var element in response.data['posts']) {
      allPosts.add(Post.fromJson(element));
    }
    return allPosts;
  }

  
  Future<List<Post>> getFollowingPosts() async {
    final response = await _dioService.get('/u/following/posts');
    
    List<Post> postsList = [];
    final responseData = response.data;
    for (var post in responseData['post_list']) {
      postsList.add(Post.fromJson(post));
    }
    return postsList;
  }

  
  Future<dynamic> createPost(String content, {String filePath = ""}) async {
    final postTitle = generateRandomTitle(32);
    
    final formData = FormData.fromMap({
      'title': postTitle,
      'content': content,
      'topic_id': '',
    });

    if (filePath.isNotEmpty) {
      formData.files.add(MapEntry(
        'file',
        await MultipartFile.fromFile(filePath),
      ));
    }

    final response = await _dioService.post('/api/user/post', data: formData);
    return response.data;
  }

  
  // Get post discussions/comments
  Future<dynamic> getPostDiscussions(String discussionId, String postId) async {
    final response = await _dioService.get('/api/discussion/$discussionId/post/$postId');
    return response.data;
  }

  Future<bool> postInDiscussion(String discussionId, String postUri, String content) async {
    final postTitle = generateRandomTitle(32);

    final response = await _dioService.post(
      '/api/discussion/$discussionId/post/$postUri/reply',
      data: {
        'title': content,
        'content': postTitle,
      },
    );
    
    return response.statusCode == 200;
  }

  
  Future<dynamic> likePost(String postId) async {
    final response = await _dioService.post('/api/post/$postId/like');
    return response.data;
  }

  
  Future<dynamic> unlikePost(String postId) async {
    final response = await _dioService.delete('/api/post/$postId/like');
    return response.data;
  }

  
  Future<dynamic> sharePost(String postId) async {
    final response = await _dioService.post('/api/post/$postId/share');
    return response.data;
  }

  
  Future<dynamic> deletePost(String postId) async {
    final response = await _dioService.delete('/api/post/$postId');
    return response.data;
  }

  
  Future<dynamic> editPost(String postId, String newContent) async {
    final response = await _dioService.put(
      '/api/post/$postId',
      data: {'content': newContent},
    );
    return response.data;
  }

  
  Future<dynamic> getPostComments(String postId) async {
    final response = await _dioService.get('/api/post/$postId/comments');
    return response.data;
  }

  
  Future<dynamic> addComment(String postId, String comment) async {
    final response = await _dioService.post(
      '/api/post/$postId/comment',
      data: {'content': comment},
    );
    return response.data;
  }
}
