import 'dart:convert';
import 'package:darve/config.dart';
import 'package:darve/api/models/user_notification_model.dart';
import 'package:darve/services/http/http_service.dart';
import 'package:darve/utils/constants.dart';
import 'package:http/http.dart' as http;

class GetNotificationsQuery {
  num? start;
  num? count;
  bool? isRead;

  GetNotificationsQuery({this.start, this.count, this.isRead});

  Map<String, String> toMap() {
    final map = <String, String>{};
    if (start != null) map['start'] = start.toString();
    if (count != null) map['count'] = count.toString();
    if (isRead != null) map['is_read'] = isRead.toString();
    return map;
  }
}

/// TODO: compare with [NotificationsRepository] and merge
class UserNotificationsApi {
  final HttpService _dioService;

  const UserNotificationsApi(this._dioService) ;

  Future<List<UserNotificationModel>> get(GetNotificationsQuery? query) async {
    final uri = Uri.parse('${AppConfig.instance.apiUrl}/api/notifications').replace(
    queryParameters: query?.toMap()
  );
    
    final response = await http.get(uri,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': 'jwt=$jwtToken',
      },
    );

    if (response.statusCode != 200) throw Exception(response.body);
    final responseData = json.decode(response.body);
    return (responseData as List)
      .map((item) => UserNotificationModel.fromMap(item))
      .toList();
  }

  Future<void> read(String id) async {
    final response = await http.post(
      Uri.parse('${AppConfig.instance.apiUrl}/api/notifications/$id/read'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
         'Cookie': 'jwt=$jwtToken',
      },
    );
    if (response.statusCode != 200) throw ArgumentError(response.body);
  }

  Future<void> readAll() async {
    final response = await http.post(
      Uri.parse('${AppConfig.instance.apiUrl}/api/notifications/read'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
         'Cookie': 'jwt=$jwtToken',
      },
    );
    if (response.statusCode != 200) throw ArgumentError(response.body);
  }
 
  Future<num> getCountOfUnread() async {
    final response = await http.get(
      Uri.parse('${AppConfig.instance.apiUrl}/api/notifications/count?is_read=false'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': 'jwt=$jwtToken',
      },
    );
    if (response.statusCode != 200) throw ArgumentError(response.body);
    return json.decode(response.body) as num;
  }

  Stream<UserNotificationModel> getSse() async* {
   final request = http.Request(
      'GET',
      Uri.parse('${AppConfig.instance.apiUrl}/api/notifications/sse'),
    );
    
    request.headers.addAll({
      'Accept': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Cookie': 'jwt=$jwtToken',
    });

    final response = await request.send();
    if (response.statusCode != 200) throw Exception(response.reasonPhrase);

    final stream = response.stream
        .transform(utf8.decoder)
        .transform(const LineSplitter());

    await for (String line in stream) {
      line = line.trim();
      if (line.isEmpty || line.startsWith(':')) continue;
      if (line.startsWith('data: ')) {
        final jsonData = line.substring(6);
        if (jsonData.isEmpty || jsonData == 'heartbeat') continue;
        final Map<String, dynamic> data = json.decode(jsonData);
        yield UserNotificationModel.fromMap(data);
      }
    }
  }

}
