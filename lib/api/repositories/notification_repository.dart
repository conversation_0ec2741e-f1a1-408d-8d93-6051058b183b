import 'dart:convert';
import 'package:darve/api/models/user_notification_model.dart';
import 'package:darve/services/http/http_service.dart';
import 'package:dio/dio.dart';

class GetNotificationsQuery {
  num? start;
  num? count;
  bool? isRead;

  GetNotificationsQuery({this.start, this.count, this.isRead});

  Map<String, String> toMap() {
    final map = <String, String>{};
    if (start != null) map['start'] = start.toString();
    if (count != null) map['count'] = count.toString();
    if (isRead != null) map['is_read'] = isRead.toString();
    return map;
  }
}

/// Notifications API using DioService for HTTP requests
/// Provides methods for fetching, reading notifications and SSE streaming
class NotificationRepository {
  final HttpService _httpService;

  const NotificationRepository(this._httpService);

  Future<List<UserNotificationModel>> get(
      {GetNotificationsQuery? query}) async {
    try {
      final response = await _httpService.get<dynamic>(
        '/api/notifications',
        queryParameters: query?.toMap(),
      );

      final responseData = response.data;
      if (responseData is List) {
        return responseData
            .map((item) => UserNotificationModel.fromMap(item as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Invalid response format: expected List, got ${responseData.runtimeType}');
      }
    } on DioException catch (e) {
      throw Exception('Failed to get notifications: ${e.message}');
    }
  }

  Future<void> read(String id) async {
    try {
      await _httpService.post('/api/notifications/$id/read');
    } on DioException catch (e) {
      throw Exception('Failed to mark notification as read: ${e.message}');
    }
  }

  Future<void> readAll() async {
    try {
      await _httpService.post('/api/notifications/read');
    } on DioException catch (e) {
      throw Exception('Failed to mark all notifications as read: ${e.message}');
    }
  }
 
  Future<num> getCountOfUnread() async {
    try {
      final response = await _httpService.get<num>(
        '/api/notifications/count',
        queryParameters: {'is_read': 'false'},
      );
      return response.data ?? 0;
    } on DioException catch (e) {
      throw Exception('Failed to get unread notifications count: ${e.message}');
    }
  }

  Stream<UserNotificationModel> getSse() async* {
    try {
      final stream = _httpService.getStream('/api/notifications/sse');

      await for (String? line in stream) {
        if (line == null || line.isEmpty || line == 'heartbeat') continue;

        try {
          final Map<String, dynamic> data = json.decode(line);
          yield UserNotificationModel.fromMap(data);
        } catch (e) {
          // Skip invalid JSON data
          continue;
        }
      }
    } catch (e) {
      throw Exception('Failed to connect to notifications SSE: $e');
    }
  }

}
