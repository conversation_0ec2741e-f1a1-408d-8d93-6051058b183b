import 'package:darve/services/http/http_service.dart';
import 'package:dio/dio.dart';

class ChallengeRepository {
  final HttpService _dioService;

  ChallengeRepository(this._dioService);

  Future<dynamic> getTaskRequests(String postId) async {
    final response = await _dioService.get('/api/task_request/list/post/$postId');
    return response.data;
  }

  
  Future<dynamic> getAllReceivedTaskRequests() async {
    final response = await _dioService.get('/api/task_request/received');
    return response.data;
  }

  
  Future<dynamic> getReceivedTaskRequestsForPost(String postId) async {
    final response = await _dioService.get('/api/task_request/received/post/$postId');
    return response.data;
  }

  
  Future<dynamic> acceptChallenge(String taskId, bool accepted) async {
    final response = await _dioService.post(
      '/api/task_request/$taskId/accept',
      data: {'accept': accepted},
    );
    return response.data;
  }

  
  Future<dynamic> deliverTaskRequest(String taskId, String filePath, String postId) async {
    final formData = FormData.fromMap({
      'post_id': postId,
      'file': await MultipartFile.fromFile(filePath),
    });

    final response = await _dioService.post(
      '/api/task_request/$taskId/deliver',
      data: formData,
    );
    return response.data;
  }

  
  Future<dynamic> createChallenge(Map<String, dynamic> challengeData) async {
    final response = await _dioService.post('/api/challenge', data: challengeData);
    return response.data;
  }

  
  Future<dynamic> getMyChallenges() async {
    final response = await _dioService.get('/api/challenges/my');
    return response.data;
  }

  
  Future<dynamic> getGivenChallenges() async {
    final response = await _dioService.get('/api/challenges/given');
    return response.data;
  }

  
  Future<dynamic> addParticipant(String challengeId, String userId) async {
    final response = await _dioService.post(
      '/api/challenge/$challengeId/participant',
      data: {'user_id': userId},
    );
    return response.data;
  }
}
