import 'package:darve/services/http/http_service.dart';
import 'package:darve/pages/utility.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class ChatRepository {
  final HttpService _dioService;

  ChatRepository(this._dioService);

  
  Future<dynamic> getChatsList() async {
    final response = await _dioService.get('/api/user_chat/list');
    return response.data;
  }

  
  Future<dynamic> createChatWithUserId(String otherUserId) async {
    final response = await _dioService.get('/api/user_chat/with/$otherUserId');
    return response.data;
  }

  
  Future<dynamic> postMessageInChat(String discussionId, String content) async {
    final postTitle = generateRandomTitle(32);
    
    final formData = FormData.fromMap({
      'title': postTitle,
      'content': content,
      'topic_id': '',
    });

    final response = await _dioService.post(
      '/api/discussion/$discussionId/post',
      data: formData,
    );
    return response.data;
  }

  
  Future<dynamic> getChatMessages(String chatId, {int limit = 50, int offset = 0}) async {
    final response = await _dioService.get(
      '/api/chat/$chatId/messages',
      queryParameters: {
        'limit': limit,
        'offset': offset,
      },
    );
    return response.data;
  }

  
  Future<dynamic> markChatAsRead(String chatId) async {
    final response = await _dioService.post('/api/chat/$chatId/read');
    return response.data;
  }

  
  Future<dynamic> deleteChatMessage(String messageId) async {
    final response = await _dioService.delete('/api/chat/message/$messageId');
    return response.data;
  }

  
  Future<dynamic> editChatMessage(String messageId, String newContent) async {
    final response = await _dioService.put(
      '/api/chat/message/$messageId',
      data: {'content': newContent},
    );
    return response.data;
  }

  Stream<String?> getChatDiscussionByIdSse(String discussionId) {
    return _dioService.getStream('/api/discussion/$discussionId/sse');
  }

  Future<List> getChatDiscussionById(String discussionId) async {
    final response = await _dioService.get(
      '/discussion/$discussionId',
    );

    return response.data as List;
  }

  Stream<dynamic> getChatListSse() async* {
    var emittedCount = 0;
    while (true) {
      emittedCount++;
      try {
        yield* _dioService.getStream('/api/events/sse');
      } catch (error) {
        debugPrint(
            'emittedCount: $emittedCount Error during getChatListSse: $error');
        yield {error: error, emittedCount: emittedCount};

        // Wait a bit before retrying to prevent rapid reconnections
        await Future.delayed(const Duration(seconds: 1));
      }
    }
  }
}
