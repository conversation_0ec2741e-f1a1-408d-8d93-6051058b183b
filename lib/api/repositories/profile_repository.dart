import 'package:darve/utils/interfaces/Follower.dart';
import 'package:darve/utils/interfaces/ProfileData.dart';
import 'package:darve/services/http/http_service.dart';

class ProfileRepository {
  final HttpService _dioService;

  ProfileRepository(this._dioService);

  Future<ProfileData> getProfileData(String username) async {
    try {
      if (username.startsWith("@")) {
        username = username.split("@")[1];
      }

      final response = await _dioService.get('/u/$username');

      if (response.data != null) {
        return ProfileData.fromJson(response.data);
      }
      return ProfileData.empty();
    } catch (e) {
      return ProfileData.empty();
    }
  }

  Future<dynamic> followUser(String userId) async {
    final response = await _dioService.post('/api/follow/$userId');
    return response.data;
  }

  Future<dynamic> unfollowUser(String userId) async {
    final response = await _dioService.post('/api/unfollow/$userId');
    return response.data;
  }

  Future<dynamic> searchUser(String userInput) async {
    final response = await _dioService.post(
      '/api/user/search',
      data: {'query': userInput},
    );

    return response.data['items'] ?? [];
  }

  Future<dynamic> getFollowers(String userId) async {
    final response = await _dioService.get('/api/user/$userId/followers');
    // Assuming responseData['list'] is a List of dynamic objects.
    List<Follower> followers = (response.data['items'] as List)
        .map((item) => Follower(
              username: item['username'],
              name: item['name'],
              imageUrl: item['image_url'],
            ))
        .toList();
    return followers;
  }

  Future<bool> isFollowing(String userId) async {
    final response = await _dioService.get('/api/user/$userId/is-following');
    return response.data['is_following'] ?? false;
  }

  Future<dynamic> editProfile(String profileData, String imagePath) async {
    final response = await _dioService.post(
      '/api/user/profile/edit',
      data: profileData,
    );
    return response.data;
  }

  Future<dynamic> getFollowing(String userId) async {
    final response = await _dioService.get('/api/user/$userId/following');
    return response.data;
  }
}
