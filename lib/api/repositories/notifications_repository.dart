import 'package:darve/services/http/http_service.dart';
import 'package:darve/utils/IdExtractor.dart';
import 'package:darve/utils/interfaces/Notification.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class NotificationsRepository {
  final HttpService _dioService;

  NotificationsRepository(this._dioService);

  
  Future<List<DarveNotification>> getNotifications() async {
    List<DarveNotification> notifications = [];
    
    final response = await _dioService.get('/api/notification/user/history');
    
    for (var notif in (response.data as List)) {
      var val = notif['event']['value'];
      String type = notif['event']['type'];

      debugPrint("type ==$type val===$val");

      if (type == DarveNotification.userFollowAdded) {
        val = UserFollowAdded(
          username: val["username"],
          followedUser: val["follows_username"],
        );
      } else if (type == DarveNotification.userTaskRequestCreated) {
        val = UserTaskRequestCreated(
          taskId: IdExtractor.getIdent(val, propName: "task_id"),
          fromUser: IdExtractor.getIdent(val, propName: "from_user"),
          toUser: IdExtractor.getIdent(val, propName: "to_user"),
        );
      }

      notifications.add(DarveNotification.fromDetails(
        IdExtractor.getIdent(notif),
        type,
        val,
      ));
    }
    
    return notifications;
  }


  Stream<dynamic> getNotificationsSse() {
    return _dioService.getStream('/api/notification/user/sse');
  }

  Future<dynamic> markNotificationAsRead(String notificationId) async {
    final response = await _dioService.post('/api/notification/$notificationId/read');
    return response.data;
  }

  
  Future<dynamic> markAllNotificationsAsRead() async {
    final response = await _dioService.post('/api/notification/read-all');
    return response.data;
  }

  
  Future<dynamic> deleteNotification(String notificationId) async {
    final response = await _dioService.delete('/api/notification/$notificationId');
    return response.data;
  }

  
  Future<dynamic> getUnreadNotificationsCount() async {
    final response = await _dioService.get('/api/notification/unread-count');
    return response.data;
  }
}
