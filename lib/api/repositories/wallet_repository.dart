import 'package:darve/services/http/http_service.dart';
import 'package:dio/dio.dart';

class WalletRepository {
  final HttpService _dioService;

  WalletRepository(this._dioService);

  
  Future<dynamic> getBalance() async {
    final response = await _dioService.get('/api/user/wallet/balance');
    return response.data;
  }

  
  Future<dynamic> getUserTxHistory({int count = 20, int start = 0}) async {
    final response = await _dioService.get(
      '/api/user/wallet/history',
      queryParameters: {
        'count': count.toString(),
        'start': start.toString(),
      },
    );
    return response.data;
  }

  
  Future<String> createPaymentIntent(String amount) async {
    final response = await _dioService.get('/api/user/wallet/endowment/$amount');
    return response.data.toString();
  }

  
  Stream<dynamic> getBalanceSse() async* {
    // Note: SSE implementation would need special handling with Dio
    // For now, we'll implement a basic version
    try {
      final dio = _dioService.dio;
      final response = await dio.get(
        '/api/user/wallet/balance/sse',
        options: Options(
          headers: {'Content-Type': 'text/event-stream'},
          responseType: ResponseType.stream,
        ),
      );

      await for (final chunk in response.data.stream) {
        final data = String.fromCharCodes(chunk);
        if (data.isNotEmpty) {
          yield data;
        }
      }
    } catch (e) {
      throw Exception('Failed to connect to balance SSE: $e');
    }
  }

  
  Future<dynamic> transferFunds(String recipientId, double amount) async {
    final response = await _dioService.post(
      '/api/user/wallet/transfer',
      data: {
        'recipient_id': recipientId,
        'amount': amount,
      },
    );
    return response.data;
  }

  
  Future<dynamic> withdrawFunds(double amount, String method) async {
    final response = await _dioService.post(
      '/api/user/wallet/withdraw',
      data: {
        'amount': amount,
        'method': method,
      },
    );
    return response.data;
  }
}
