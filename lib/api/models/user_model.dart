// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:darve/api/models/server_id.dart';

class UserModel {
  final String id;
  final String username;
  final String? name;
  final String? email;
  final String? bio;
  final String? imageUri;

  UserModel(
      {required this.id,
      required this.username,
      required this.name,
      this.email,
      this.bio,
      required this.imageUri});

  factory UserModel.fromMap(Map<String, dynamic> json) {
    return UserModel(
      id: ServerId.fromMap(json['id']).toString(),
      username: json['username'] as String,
      name: json['full_name'] as String?,
      email: json['email_verified'] as String?,
      bio: json['bio'] as String?,
      imageUri: json['image_uri'] as String?,
    );
  }

  String toJson() {
    return '{"id": "$id", "username": "$username", "full_name": "$name", "email_verified": ${email != null ? '"$email"' : 'null'}, "bio": ${bio != null ? '"$bio"' : 'null'}, "image_uri": "$imageUri"}';
  }

  factory UserModel.fromJson(String jsonString) {
    final Map<String, dynamic> json = jsonDecode(jsonString);
    return UserModel(
      id: json['id'] as String,
      username: json['username'] as String,
      name: json['full_name'] as String,
      email: json['email_verified'] as String?,
      bio: json['bio'] as String?,
      imageUri: json['image_uri'] as String,
    );
  }
}
