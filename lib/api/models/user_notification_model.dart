class UserNotificationModel {
  final String id;
  final UserNotificationEvent event;
  final String title;
  final bool isRead;
  final String createdBy;
  final dynamic metadata;

  UserNotificationModel(
      {required this.id,
      required this.event,
      required this.title,
      required this.isRead,
      required this.createdBy,
      this.metadata});

  factory UserNotificationModel.fromMap(Map<String, dynamic> json) {
    final String rawEvent = json['event'] as String;
    final String normalizedEvent = rawEvent[0].toLowerCase() + rawEvent.substring(1);
    return UserNotificationModel(
      id: json['id'].toString(),
      event: UserNotificationEvent.values.byName(normalizedEvent),
      title: json['title'] as String,
      isRead: json['is_read'] as bool,
      createdBy: json["created_by"].toString(),
      metadata: json['metadata']
    );
  }
}

enum UserNotificationEvent {
  userBalanceUpdate,
  userChatMessage,
  userCommunityPost,
  userFollowAdded,
  userLikePost,
  userTaskRequestCreated,
  userTaskRequestDelivered,
  userTaskRequestReceived,
}

