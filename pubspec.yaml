name: darve
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.4.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  font_awesome_flutter: ^10.7.0
  mobx: ^2.1.0
  flutter_mobx: ^2.1.0
  shared_preferences: ^2.0.15
  intl: ^0.19.0
  http:
  provider:
  url_launcher: ^6.3.1
  file_picker: ^8.1.6
  html: ^0.15.5
  dotted_border: ^2.1.0
  sliding_up_panel2: ^3.3.0+1
  camera: ^0.11.1
  path_provider: ^2.1.5
  video_player: ^2.9.5
  flutter_stripe: ^11.5.0
  get: ^4.7.2
  pinput: ^5.0.1
  sign_in_with_apple: ^7.0.1
  flutter_dotenv: ^5.2.1
  google_sign_in: ^6.3.0
  flutter_facebook_auth: ^6.0.4
  dio: ^5.8.0+1
  get_it: ^8.0.3

dev_dependencies:
  flutter_launcher_icons: ^0.13.1
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0
  build_runner: ^2.0.0
  mobx_codegen: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/darve.png"

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - .env 
    - assets/images/
    - assets/images/sign_in/
    - assets/images/settings/
    - assets/images/profile/
    - assets/images/home/
    - assets/images/challenges/
    - assets/wallet/
    - assets/images/navigation/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

  fonts:
    - family: Rubik
      fonts:
        - asset: assets/fonts/Rubik-Regular.ttf
        - asset: assets/fonts/Rubik-Bold.ttf
          weight: 700
        - asset: assets/fonts/Rubik-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Rubik-Medium.ttf
          weight: 500
        - asset: assets/fonts/Rubik-Light.ttf
          weight: 300
        
