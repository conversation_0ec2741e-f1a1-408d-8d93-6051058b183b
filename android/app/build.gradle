plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty("flutter.versionCode", "1").toInteger()
def flutterVersionName = localProperties.getProperty("flutter.versionName", "1.0")

android {
    namespace = "com.darveapp.mobile"
    compileSdk = 35
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    configurations.all {
        exclude group: 'com.google.errorprone', module: 'error_prone_annotations'
    resolutionStrategy {
        force 'androidx.camera:camera-core:1.3.0'
        force 'androidx.camera:camera-camera2:1.3.0'
        force 'androidx.camera:camera-lifecycle:1.3.0'
        force 'androidx.camera:camera-video:1.3.0'
        force 'androidx.camera:camera-view:1.3.0'
    }
}

    defaultConfig {
        applicationId = "com.darveapp.mobile"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode
        versionName flutterVersionName
        multiDexEnabled = true
    }

 buildTypes {
    release {
        signingConfig signingConfigs.debug
        minifyEnabled false // Set to true if using ProGuard
        shrinkResources false // Set to true only if minifyEnabled is true
        proguardFiles getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
    }
}

}

flutter {
    source = "../.."
}
dependencies {
 implementation 'androidx.camera:camera-core:1.3.0'
    implementation 'androidx.camera:camera-camera2:1.3.0'
    implementation 'androidx.camera:camera-lifecycle:1.3.0'
    implementation 'androidx.camera:camera-video:1.3.0'
    implementation 'androidx.camera:camera-view:1.3.0'
    implementation 'androidx.multidex:multidex:2.0.1'
}

